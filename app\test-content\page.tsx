"use client"

import { useEffect, useState } from 'react'
import { useContent } from '@/hooks/use-stock'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { BookOpen, Users, Scale, MessageSquare, AlertTriangle, CheckCircle } from 'lucide-react'

export default function TestContentPage() {
  const { 
    reglementSections, 
    loreChapters, 
    ticketCategories,
    loading, 
    error, 
    fetchReglementSections, 
    fetchLoreChapters, 
    fetchTicketCategories 
  } = useContent()

  const [testResults, setTestResults] = useState<string[]>([])

  useEffect(() => {
    fetchReglementSections()
    fetchLoreChapters()
    fetchTicketCategories()
  }, [fetchReglementSections, fetchLoreChapters, fetchTicketCategories])

  const addTestResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`])
  }

  const runTests = () => {
    setTestResults([])
    addTestResult('🧪 Starting content tests...')
    
    // Test règlement sections
    if (reglementSections.length > 0) {
      addTestResult(`✅ Règlement: ${reglementSections.length} sections loaded`)
      reglementSections.forEach(section => {
        addTestResult(`  📋 Section: ${section.title} (${section.slug})`)
      })
    } else {
      addTestResult('❌ Règlement: No sections found')
    }

    // Test lore chapters
    if (loreChapters.length > 0) {
      addTestResult(`✅ Lore: ${loreChapters.length} chapters loaded`)
      loreChapters.forEach(chapter => {
        addTestResult(`  📖 Chapter ${chapter.chapter_number}: ${chapter.title}`)
      })
    } else {
      addTestResult('❌ Lore: No chapters found')
    }

    // Test ticket categories
    if (ticketCategories.length > 0) {
      addTestResult(`✅ Tickets: ${ticketCategories.length} categories loaded`)
      ticketCategories.forEach(category => {
        addTestResult(`  🎫 Category: ${category.name} (${category.color})`)
      })
    } else {
      addTestResult('❌ Tickets: No categories found')
    }

    addTestResult('🎉 Content tests completed!')
  }

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <h1 className="text-3xl font-bold mb-6">Content Management Test</h1>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Content Overview */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BookOpen className="h-5 w-5" />
                Règlement Sections
              </CardTitle>
              <CardDescription>
                {loading ? 'Loading...' : `${reglementSections.length} sections loaded`}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {reglementSections.length > 0 ? (
                <div className="space-y-2">
                  {reglementSections.map(section => (
                    <div key={section.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                      <span className="font-medium">{section.title}</span>
                      <Badge variant={section.is_published ? "default" : "secondary"}>
                        {section.is_published ? 'Published' : 'Draft'}
                      </Badge>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500">No sections found</p>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Lore Chapters
              </CardTitle>
              <CardDescription>
                {loading ? 'Loading...' : `${loreChapters.length} chapters loaded`}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loreChapters.length > 0 ? (
                <div className="space-y-2">
                  {loreChapters.map(chapter => (
                    <div key={chapter.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                      <span className="font-medium">Ch. {chapter.chapter_number}: {chapter.title}</span>
                      <Badge variant={chapter.is_published ? "default" : "secondary"}>
                        {chapter.is_published ? 'Published' : 'Draft'}
                      </Badge>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500">No chapters found</p>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MessageSquare className="h-5 w-5" />
                Ticket Categories
              </CardTitle>
              <CardDescription>
                {loading ? 'Loading...' : `${ticketCategories.length} categories loaded`}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {ticketCategories.length > 0 ? (
                <div className="space-y-2">
                  {ticketCategories.map(category => (
                    <div key={category.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                      <div className="flex items-center gap-2">
                        <div 
                          className="w-3 h-3 rounded-full" 
                          style={{ backgroundColor: category.color || '#gray' }}
                        ></div>
                        <span className="font-medium">{category.name}</span>
                      </div>
                      <Badge variant={category.is_active ? "default" : "secondary"}>
                        {category.is_active ? 'Active' : 'Inactive'}
                      </Badge>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500">No categories found</p>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Test Results */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Scale className="h-5 w-5" />
                Test Controls
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button onClick={runTests} className="w-full" disabled={loading}>
                {loading ? 'Loading...' : 'Run Content Tests'}
              </Button>
              
              {error && (
                <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded">
                  <AlertTriangle className="h-4 w-4 text-red-500" />
                  <span className="text-red-700 text-sm">{error}</span>
                </div>
              )}
              
              {!loading && !error && (
                <div className="flex items-center gap-2 p-3 bg-green-50 border border-green-200 rounded">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span className="text-green-700 text-sm">Content loaded successfully</span>
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Test Results</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="bg-gray-900 text-green-400 p-4 rounded font-mono text-sm h-64 overflow-y-auto">
                {testResults.length === 0 ? (
                  <div className="text-gray-500">No tests run yet. Click "Run Content Tests" to start.</div>
                ) : (
                  testResults.map((result, index) => (
                    <div key={index} className="mb-1">
                      {result}
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button variant="outline" className="w-full" asChild>
                <a href="/reglement">View Règlement Page</a>
              </Button>
              <Button variant="outline" className="w-full" asChild>
                <a href="/lore">View Lore Page</a>
              </Button>
              <Button variant="outline" className="w-full" asChild>
                <a href="/test-db">Database Connection Test</a>
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
