# 📦 Stock Management Database Setup

This directory contains the database schema and setup instructions for the stock management system of "Le Royaume Des Tempêtes".

## 🗄️ Database Architecture

The stock management system uses **Supabase** (PostgreSQL) as the database backend and **Logto** for user authentication. The database only stores stock/inventory data, while user management is handled entirely by Logto.

### 📋 Tables Overview

1. **`stock_categories`** - Product categories (Armes, Armures, Potions, etc.)
2. **`stock_items`** - Individual inventory items with quantities and details
3. **`stock_transactions`** - History of all stock movements (in/out/adjustments)
4. **`stock_alerts`** - Automated alerts for low stock, out of stock, etc.

### 🔐 User Management

- **Authentication**: Handled by <PERSON>gt<PERSON> (no user tables in this database)
- **User References**: Stored as Logto user ID strings in relevant tables
- **Permissions**: Managed through Logto roles and claims

## 🚀 Setup Instructions

### 1. Supabase Project Setup

1. Create a new Supabase project at [supabase.com](https://supabase.com)
2. Note down your project URL and API keys
3. Go to the SQL Editor in your Supabase dashboard

### 2. Database Schema Installation

1. Copy the contents of `schema.sql`
2. Paste it into the Supabase SQL Editor
3. Execute the script to create all tables, indexes, and sample data

### 3. Environment Configuration

1. Copy `.env.example` to `.env.local`
2. Fill in your Supabase credentials:

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
```

### 4. Row Level Security (RLS)

The schema includes basic RLS policies that allow all operations. For production, you should:

1. Configure Logto JWT validation in Supabase
2. Update RLS policies to check user roles and permissions
3. Implement proper access control based on Logto claims

Example RLS policy for admin-only access:
```sql
CREATE POLICY "Admin only access" ON stock_items
FOR ALL USING (
  auth.jwt() ->> 'role' = 'admin'
);
```

## 📊 Sample Data

The schema includes sample data for:

- **10 default categories** (Armes, Armures, Potions, etc.)
- **6 sample stock items** with realistic gaming inventory
- **Proper relationships** between items and categories

## 🔧 API Endpoints

The following API endpoints are available:

### Stock Items
- `GET /api/stock/items` - List all items with filtering
- `POST /api/stock/items` - Create new item
- `GET /api/stock/items/[id]` - Get single item
- `PUT /api/stock/items/[id]` - Update item
- `DELETE /api/stock/items/[id]` - Delete item
- `PATCH /api/stock/items/[id]` - Update quantity with transaction

### Categories
- `GET /api/stock/categories` - List all categories

### Alerts
- `GET /api/stock/alerts` - List alerts (with unread filter)
- `PATCH /api/stock/alerts` - Mark alert as read

## 🎮 Gaming Context

This stock management system is designed for a gaming server with:

- **Fantasy RPG items** (weapons, armor, potions, materials)
- **In-game currency** (Shinbans)
- **Physical locations** (Armurerie, Alchimie, Entrepôt)
- **Gaming-specific categories** and item types

## 🔄 Stock Operations

### Automatic Alerts
The system automatically creates alerts when:
- Stock falls below minimum quantity (low_stock)
- Stock reaches zero (out_of_stock)
- Stock exceeds maximum quantity (overstock)

### Transaction Types
- **`in`** - Stock received/added
- **`out`** - Stock sold/removed
- **`adjustment`** - Manual quantity correction
- **`transfer`** - Movement between locations

### Stock Status
- **`active`** - Available for use
- **`inactive`** - Temporarily disabled
- **`discontinued`** - No longer available

## 🛡️ Security Considerations

1. **Never expose service role key** in client-side code
2. **Use RLS policies** for data access control
3. **Validate all inputs** on the server side
4. **Implement proper error handling** for database operations
5. **Use Logto claims** for role-based access control

## 📈 Performance Optimization

The schema includes indexes on:
- Category lookups
- Status filtering
- SKU searches
- Quantity ranges
- Transaction history
- Unread alerts

## 🔧 Maintenance

### Regular Tasks
1. **Monitor alerts** for stock issues
2. **Review transactions** for unusual patterns
3. **Update categories** as needed
4. **Backup database** regularly
5. **Optimize queries** based on usage patterns

### Scaling Considerations
- Consider partitioning transaction table by date
- Implement archiving for old transactions
- Monitor database performance and add indexes as needed
- Consider read replicas for reporting queries
