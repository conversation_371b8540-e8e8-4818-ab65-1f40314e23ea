"use client"

import { useState } from 'react'
import { supabase } from '@/lib/supabase'

const SQL_SCRIPTS = {
  dropOldTables: `
-- Drop old stock management tables
DROP VIEW IF EXISTS stock_summary CASCADE;
DROP VIEW IF EXISTS stock_items_with_category CASCADE;
DROP TABLE IF EXISTS stock_alerts CASCADE;
DROP TABLE IF EXISTS stock_transactions CASCADE;
DROP TABLE IF EXISTS stock_items CASCADE;
DROP TABLE IF EXISTS stock_categories CASCADE;
  `,
  
  createTicketCategories: `
CREATE TABLE IF NOT EXISTS ticket_categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    color VARCHAR(7),
    icon VARCHAR(50),
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
  `,
  
  createReglementSections: `
CREATE TABLE IF NOT EXISTS reglement_sections (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    order_index INTEGER NOT NULL DEFAULT 0,
    is_published BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by VARCHAR(255),
    updated_by VARCHAR(255)
);
  `,
  
  createLoreChapters: `
CREATE TABLE IF NOT EXISTS lore_chapters (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL UNIQUE,
    content TEXT NOT NULL,
    excerpt TEXT,
    chapter_number INTEGER NOT NULL,
    is_published BOOLEAN NOT NULL DEFAULT false,
    featured_image VARCHAR(500),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by VARCHAR(255),
    updated_by VARCHAR(255)
);
  `,
  
  insertSampleData: `
-- Insert ticket categories
INSERT INTO ticket_categories (name, description, color, icon) VALUES
('Bug Report', 'Signalement de bugs et problèmes techniques', '#FF6B6B', 'Bug'),
('Feature Request', 'Demandes de nouvelles fonctionnalités', '#4ECDC4', 'Lightbulb'),
('Support', 'Demandes d''aide et support général', '#45B7D1', 'HelpCircle'),
('Modération', 'Questions liées à la modération', '#96CEB4', 'Shield')
ON CONFLICT (name) DO NOTHING;

-- Insert règlement sections
INSERT INTO reglement_sections (title, slug, description, order_index, is_published) VALUES
('Règles Générales', 'regles-generales', 'Règles de base du serveur Discord', 1, true),
('Règles de Chat', 'regles-chat', 'Règles spécifiques aux canaux de discussion', 2, true)
ON CONFLICT (slug) DO NOTHING;
  `
}

export default function SetupDatabasePage() {
  const [results, setResults] = useState<string[]>([])
  const [isRunning, setIsRunning] = useState(false)

  const addResult = (message: string) => {
    setResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`])
  }

  const runScript = async (scriptName: keyof typeof SQL_SCRIPTS, description: string) => {
    try {
      addResult(`🚀 Starting: ${description}`)
      
      // For now, we'll just try to create tables using the Supabase client
      // Since direct SQL execution isn't working
      
      if (scriptName === 'createTicketCategories') {
        const { error } = await supabase.from('ticket_categories').select('*').limit(1)
        if (error && error.message.includes('does not exist')) {
          addResult(`❌ Table ticket_categories doesn't exist - needs manual creation`)
        } else {
          addResult(`✅ Table ticket_categories is accessible`)
        }
      }
      
      if (scriptName === 'createReglementSections') {
        const { error } = await supabase.from('reglement_sections').select('*').limit(1)
        if (error && error.message.includes('does not exist')) {
          addResult(`❌ Table reglement_sections doesn't exist - needs manual creation`)
        } else {
          addResult(`✅ Table reglement_sections is accessible`)
        }
      }
      
      if (scriptName === 'createLoreChapters') {
        const { error } = await supabase.from('lore_chapters').select('*').limit(1)
        if (error && error.message.includes('does not exist')) {
          addResult(`❌ Table lore_chapters doesn't exist - needs manual creation`)
        } else {
          addResult(`✅ Table lore_chapters is accessible`)
        }
      }
      
      if (scriptName === 'insertSampleData') {
        // Try to insert sample data
        const { error: categoryError } = await supabase
          .from('ticket_categories')
          .upsert([
            { name: 'Bug Report', description: 'Signalement de bugs', color: '#FF6B6B', icon: 'Bug' }
          ], { onConflict: 'name' })
        
        if (categoryError) {
          addResult(`❌ Failed to insert sample data: ${categoryError.message}`)
        } else {
          addResult(`✅ Sample data inserted successfully`)
        }
      }
      
    } catch (error) {
      addResult(`❌ Error in ${description}: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  const runAllSetup = async () => {
    setIsRunning(true)
    setResults([])
    
    addResult('🏁 Starting database setup...')
    
    await runScript('createTicketCategories', 'Create ticket_categories table')
    await runScript('createReglementSections', 'Create reglement_sections table') 
    await runScript('createLoreChapters', 'Create lore_chapters table')
    await runScript('insertSampleData', 'Insert sample data')
    
    addResult('🎉 Setup process completed!')
    addResult('📋 If tables are missing, please create them manually in Supabase SQL Editor')
    
    setIsRunning(false)
  }

  const copyToClipboard = (scriptName: keyof typeof SQL_SCRIPTS) => {
    navigator.clipboard.writeText(SQL_SCRIPTS[scriptName])
    addResult(`📋 Copied ${scriptName} SQL to clipboard`)
  }

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <h1 className="text-3xl font-bold mb-6">Database Setup</h1>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Control Panel */}
        <div className="space-y-6">
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">Automated Setup</h2>
            <button
              onClick={runAllSetup}
              disabled={isRunning}
              className="bg-blue-600 text-white px-6 py-3 rounded hover:bg-blue-700 disabled:opacity-50"
            >
              {isRunning ? 'Running Setup...' : 'Run Database Setup'}
            </button>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">Manual SQL Scripts</h2>
            <div className="space-y-2">
              {Object.entries(SQL_SCRIPTS).map(([key, _]) => (
                <button
                  key={key}
                  onClick={() => copyToClipboard(key as keyof typeof SQL_SCRIPTS)}
                  className="block w-full text-left bg-gray-100 hover:bg-gray-200 px-3 py-2 rounded text-sm"
                >
                  📋 Copy {key}
                </button>
              ))}
            </div>
          </div>

          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
            <h3 className="font-semibold text-yellow-800 mb-2">Manual Setup Instructions</h3>
            <ol className="text-yellow-700 text-sm space-y-1">
              <li>1. Go to your Supabase Dashboard</li>
              <li>2. Navigate to SQL Editor</li>
              <li>3. Copy and paste the SQL from database/manual-setup.sql</li>
              <li>4. Run the script to create all tables</li>
              <li>5. Come back here to test the connection</li>
            </ol>
          </div>
        </div>

        {/* Results Panel */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">Setup Results</h2>
          <div className="bg-gray-900 text-green-400 p-4 rounded font-mono text-sm h-96 overflow-y-auto">
            {results.length === 0 ? (
              <div className="text-gray-500">No results yet. Click "Run Database Setup" to start.</div>
            ) : (
              results.map((result, index) => (
                <div key={index} className="mb-1">
                  {result}
                </div>
              ))
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
