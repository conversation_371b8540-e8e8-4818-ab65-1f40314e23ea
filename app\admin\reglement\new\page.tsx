"use client"

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from "@/components/ui/breadcrumb"
import { 
  Save,
  ArrowLeft,
  AlertTriangle,
  CheckCircle,
  Loader2,
  FileText
} from "lucide-react"
import { motion } from "framer-motion"
import { useAdminContent } from "@/hooks/use-stock"

export default function NewReglementSectionPage() {
  const router = useRouter()
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)

  const { createReglementSection } = useAdminContent()

  const [formData, setFormData] = useState({
    title: '',
    slug: '',
    description: '',
    order_index: 0,
    is_published: false
  })

  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '')
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim()
  }

  const handleTitleChange = (title: string) => {
    setFormData(prev => ({
      ...prev,
      title,
      slug: prev.slug || generateSlug(title)
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    try {
      setSaving(true)
      setError(null)
      
      await createReglementSection(formData)
      setSuccess('Section créée avec succès')
      
      // Rediriger vers la page de gestion après 2 secondes
      setTimeout(() => {
        router.push('/admin/reglement')
      }, 2000)
      
    } catch (err) {
      setError('Erreur lors de la création de la section')
    } finally {
      setSaving(false)
    }
  }

  return (
    <div>
      <header className="flex h-16 shrink-0 items-center gap-2 border-b border-base-300/50 bg-base-100/95 backdrop-blur px-4 shadow-sm">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/admin" className="text-base-content/60 hover:text-base-content">
                Administration
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink href="/admin/reglement" className="text-base-content/60 hover:text-base-content">
                Règlement
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage className="text-base-content font-medium">Nouvelle Section</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
        
        <div className="ml-auto">
          <Button variant="outline" size="sm" asChild>
            <a href="/admin/reglement">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Retour
            </a>
          </Button>
        </div>
      </header>

      <main className="p-6 max-w-4xl mx-auto">
        {/* Messages */}
        {error && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center gap-2"
          >
            <AlertTriangle className="h-5 w-5 text-red-500" />
            <span className="text-red-700">{error}</span>
            <Button variant="ghost" size="sm" onClick={() => setError(null)} className="ml-auto">
              ×
            </Button>
          </motion.div>
        )}

        {success && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg flex items-center gap-2"
          >
            <CheckCircle className="h-5 w-5 text-green-500" />
            <span className="text-green-700">{success}</span>
          </motion.div>
        )}

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <Card className="bg-base-100/95 backdrop-blur border-primary/20">
            <CardHeader>
              <div className="flex items-center gap-3">
                <FileText className="h-6 w-6 text-primary" />
                <div>
                  <CardTitle className="text-2xl">Nouvelle Section de Règlement</CardTitle>
                  <CardDescription>
                    Créez une nouvelle section pour organiser vos règles
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="title">Titre de la section</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => handleTitleChange(e.target.value)}
                    placeholder="Ex: Règles Générales"
                    required
                  />
                  <p className="text-sm text-base-content/60">
                    Le titre principal de cette section de règlement
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="slug">Slug (URL)</Label>
                  <Input
                    id="slug"
                    value={formData.slug}
                    onChange={(e) => setFormData(prev => ({ ...prev, slug: e.target.value }))}
                    placeholder="regles-generales"
                    required
                  />
                  <p className="text-sm text-base-content/60">
                    Identifiant unique pour l'URL (généré automatiquement depuis le titre)
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Description de cette section de règlement..."
                    rows={4}
                  />
                  <p className="text-sm text-base-content/60">
                    Une description courte qui explique le contenu de cette section
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="order">Ordre d'affichage</Label>
                  <Input
                    id="order"
                    type="number"
                    value={formData.order_index}
                    onChange={(e) => setFormData(prev => ({ ...prev, order_index: parseInt(e.target.value) || 0 }))}
                    min="0"
                    placeholder="0"
                  />
                  <p className="text-sm text-base-content/60">
                    Position de cette section dans la liste (0 = première position)
                  </p>
                </div>

                <Separator />

                <div className="flex items-center justify-between p-4 bg-base-200/50 rounded-lg">
                  <div className="space-y-1">
                    <Label htmlFor="published" className="text-base font-medium">
                      Publier cette section
                    </Label>
                    <p className="text-sm text-base-content/60">
                      La section sera visible sur le site public
                    </p>
                  </div>
                  <Switch
                    id="published"
                    checked={formData.is_published}
                    onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_published: checked }))}
                  />
                </div>

                <Separator />

                <div className="flex gap-3 pt-4">
                  <Button type="submit" disabled={saving} className="flex-1">
                    {saving ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Création en cours...
                      </>
                    ) : (
                      <>
                        <Save className="h-4 w-4 mr-2" />
                        Créer la section
                      </>
                    )}
                  </Button>
                  <Button type="button" variant="outline" asChild>
                    <a href="/admin/reglement">
                      Annuler
                    </a>
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </motion.div>

        {/* Aide */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="mt-6"
        >
          <Card className="bg-blue-50/50 border-blue-200">
            <CardHeader>
              <CardTitle className="text-lg text-blue-800">💡 Conseils</CardTitle>
            </CardHeader>
            <CardContent className="text-blue-700 space-y-2">
              <p>• Utilisez des titres clairs et descriptifs pour vos sections</p>
              <p>• L'ordre d'affichage détermine la position dans la navigation</p>
              <p>• Vous pouvez créer la section en brouillon et la publier plus tard</p>
              <p>• Après création, vous pourrez ajouter des règles spécifiques à cette section</p>
            </CardContent>
          </Card>
        </motion.div>
      </main>
    </div>
  )
}
