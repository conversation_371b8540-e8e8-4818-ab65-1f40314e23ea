"use client"

import { useState } from 'react'

const CREATE_TABLE_QUERIES = [
  {
    name: 'ticket_categories',
    sql: `CREATE TABLE IF NOT EXISTS ticket_categories (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      name VARCHAR(255) NOT NULL UNIQUE,
      description TEXT,
      color VARCHAR(7),
      icon VARCHAR(50),
      is_active BOOLEAN NOT NULL DEFAULT true,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );`
  },
  {
    name: 'reglement_sections',
    sql: `CREATE TABLE IF NOT EXISTS reglement_sections (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      title VARCHAR(255) NOT NULL,
      slug VARCHAR(255) NOT NULL UNIQUE,
      description TEXT,
      order_index INTEGER NOT NULL DEFAULT 0,
      is_published BOOLEAN NOT NULL DEFAULT false,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      created_by VARCHAR(255),
      updated_by VARCHAR(255)
    );`
  },
  {
    name: 'reglement_rules',
    sql: `CREATE TABLE IF NOT EXISTS reglement_rules (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      section_id UUID NOT NULL REFERENCES reglement_sections(id) ON DELETE CASCADE,
      title VARCHAR(255) NOT NULL,
      content TEXT NOT NULL,
      order_index INTEGER NOT NULL DEFAULT 0,
      is_published BOOLEAN NOT NULL DEFAULT false,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      created_by VARCHAR(255),
      updated_by VARCHAR(255)
    );`
  },
  {
    name: 'lore_chapters',
    sql: `CREATE TABLE IF NOT EXISTS lore_chapters (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      title VARCHAR(255) NOT NULL,
      slug VARCHAR(255) NOT NULL UNIQUE,
      content TEXT NOT NULL,
      excerpt TEXT,
      chapter_number INTEGER NOT NULL,
      is_published BOOLEAN NOT NULL DEFAULT false,
      featured_image VARCHAR(500),
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      created_by VARCHAR(255),
      updated_by VARCHAR(255)
    );`
  },
  {
    name: 'tickets',
    sql: `CREATE TABLE IF NOT EXISTS tickets (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      title VARCHAR(255) NOT NULL,
      description TEXT NOT NULL,
      category_id UUID NOT NULL REFERENCES ticket_categories(id) ON DELETE RESTRICT,
      status VARCHAR(20) NOT NULL DEFAULT 'open' CHECK (status IN ('open', 'in_progress', 'resolved', 'closed')),
      priority VARCHAR(10) NOT NULL DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
      created_by VARCHAR(255) NOT NULL,
      assigned_to VARCHAR(255),
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      resolved_at TIMESTAMP WITH TIME ZONE,
      resolved_by VARCHAR(255)
    );`
  },
  {
    name: 'ticket_comments',
    sql: `CREATE TABLE IF NOT EXISTS ticket_comments (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      ticket_id UUID NOT NULL REFERENCES tickets(id) ON DELETE CASCADE,
      content TEXT NOT NULL,
      is_internal BOOLEAN NOT NULL DEFAULT false,
      created_by VARCHAR(255) NOT NULL,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );`
  },
  {
    name: 'admin_users',
    sql: `CREATE TABLE IF NOT EXISTS admin_users (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      discord_id VARCHAR(255) NOT NULL UNIQUE,
      username VARCHAR(255) NOT NULL,
      avatar_url VARCHAR(500),
      role VARCHAR(20) NOT NULL DEFAULT 'moderator' CHECK (role IN ('admin', 'moderator', 'editor')),
      permissions JSONB,
      is_active BOOLEAN NOT NULL DEFAULT true,
      last_login TIMESTAMP WITH TIME ZONE,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );`
  }
]

const SAMPLE_DATA_QUERIES = [
  {
    name: 'Insert ticket categories',
    sql: `INSERT INTO ticket_categories (name, description, color, icon) VALUES
      ('Bug Report', 'Signalement de bugs et problèmes techniques', '#FF6B6B', 'Bug'),
      ('Feature Request', 'Demandes de nouvelles fonctionnalités', '#4ECDC4', 'Lightbulb'),
      ('Support', 'Demandes d''aide et support général', '#45B7D1', 'HelpCircle'),
      ('Modération', 'Questions liées à la modération', '#96CEB4', 'Shield'),
      ('Événement', 'Organisation et gestion d''événements', '#FFEAA7', 'Calendar'),
      ('Autre', 'Autres demandes non catégorisées', '#A0A0A0', 'MessageSquare')
      ON CONFLICT (name) DO NOTHING;`
  },
  {
    name: 'Insert règlement sections',
    sql: `INSERT INTO reglement_sections (title, slug, description, order_index, is_published) VALUES
      ('Règles Générales', 'regles-generales', 'Règles de base du serveur Discord', 1, true),
      ('Règles de Chat', 'regles-chat', 'Règles spécifiques aux canaux de discussion', 2, true),
      ('Règles de Jeu', 'regles-jeu', 'Règles pour les activités de jeu', 3, true),
      ('Sanctions', 'sanctions', 'Système de sanctions et procédures', 4, true)
      ON CONFLICT (slug) DO NOTHING;`
  },
  {
    name: 'Insert sample lore chapters',
    sql: `INSERT INTO lore_chapters (title, slug, content, excerpt, chapter_number, is_published, featured_image) VALUES
      ('La Fondation du Royaume', 'fondation-royaume', 
       'Il y a des siècles, dans les terres mystérieuses où les tempêtes magiques dansent éternellement...', 
       'L''histoire de la création du Royaume des Tempêtes et de ses premiers dirigeants.', 
       1, true, '/images/chapter1.jpg'),
      ('Les Grandes Tempêtes', 'grandes-tempetes', 
       'Les tempêtes qui donnent son nom au royaume ne sont pas ordinaires...', 
       'Découvrez les mystères des tempêtes magiques qui façonnent le royaume.', 
       2, true, '/images/chapter2.jpg')
      ON CONFLICT (slug) DO NOTHING;`
  }
]

export default function CreateTablesPage() {
  const [results, setResults] = useState<string[]>([])
  const [isRunning, setIsRunning] = useState(false)

  const addResult = (message: string) => {
    setResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`])
  }

  const executeSQL = async (sql: string, description: string) => {
    try {
      const response = await fetch('/api/execute-sql', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ sql, description })
      })

      const result = await response.json()
      
      if (result.success) {
        addResult(`✅ ${description} - Success`)
      } else {
        addResult(`❌ ${description} - ${result.error}`)
      }
    } catch (error) {
      addResult(`❌ ${description} - ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  const insertSampleData = async (dataType: string) => {
    try {
      const response = await fetch('/api/insert-sample-data', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ dataType })
      })

      const result = await response.json()

      if (result.success) {
        addResult(`✅ ${dataType} - Sample data inserted successfully`)
      } else {
        addResult(`❌ ${dataType} - ${result.error}`)
      }
    } catch (error) {
      addResult(`❌ ${dataType} - ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  const createAllTables = async () => {
    setIsRunning(true)
    setResults([])

    addResult('🚀 Starting table creation...')

    // Create tables in order
    for (const query of CREATE_TABLE_QUERIES) {
      await executeSQL(query.sql, `Create table: ${query.name}`)
    }

    addResult('📊 Creating sample data...')

    // Insert sample data using the new API
    await insertSampleData('ticket_categories')
    await insertSampleData('reglement_sections')
    await insertSampleData('lore_chapters')

    addResult('🎉 Database setup completed!')
    setIsRunning(false)
  }

  const insertAllSampleData = async () => {
    setIsRunning(true)
    addResult('📊 Inserting sample data...')

    await insertSampleData('ticket_categories')
    await insertSampleData('reglement_sections')
    await insertSampleData('lore_chapters')

    addResult('🎉 Sample data insertion completed!')
    setIsRunning(false)
  }

  const copyAllSQL = () => {
    const allSQL = [
      ...CREATE_TABLE_QUERIES.map(q => q.sql),
      ...SAMPLE_DATA_QUERIES.map(q => q.sql)
    ].join('\n\n')
    
    navigator.clipboard.writeText(allSQL)
    addResult('📋 All SQL copied to clipboard!')
  }

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <h1 className="text-3xl font-bold mb-6">Create Database Tables</h1>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Control Panel */}
        <div className="space-y-6">
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">Quick Setup</h2>
            <div className="space-y-3">
              <button
                onClick={createAllTables}
                disabled={isRunning}
                className="w-full bg-blue-600 text-white px-6 py-3 rounded hover:bg-blue-700 disabled:opacity-50"
              >
                {isRunning ? 'Creating Tables...' : 'Create All Tables & Sample Data'}
              </button>

              <button
                onClick={insertAllSampleData}
                disabled={isRunning}
                className="w-full bg-green-600 text-white px-6 py-3 rounded hover:bg-green-700 disabled:opacity-50"
              >
                {isRunning ? 'Inserting Data...' : 'Insert Sample Data Only'}
              </button>

              <button
                onClick={copyAllSQL}
                disabled={isRunning}
                className="w-full bg-gray-600 text-white px-6 py-3 rounded hover:bg-gray-700 disabled:opacity-50"
              >
                📋 Copy All SQL to Clipboard
              </button>
            </div>
          </div>

          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
            <h3 className="font-semibold text-yellow-800 mb-2">Manual Alternative</h3>
            <p className="text-yellow-700 text-sm mb-3">
              If the automated creation doesn't work, you can:
            </p>
            <ol className="text-yellow-700 text-sm space-y-1">
              <li>1. Click "Copy All SQL to Clipboard"</li>
              <li>2. Go to your Supabase SQL Editor</li>
              <li>3. Paste and run the SQL</li>
              <li>4. Return to test the connection</li>
            </ol>
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
            <h3 className="font-semibold text-blue-800 mb-2">What This Creates</h3>
            <ul className="text-blue-700 text-sm space-y-1">
              <li>• ticket_categories - For organizing support tickets</li>
              <li>• reglement_sections - For rules organization</li>
              <li>• reglement_rules - Individual rules content</li>
              <li>• lore_chapters - Paginated lore content</li>
              <li>• tickets - Main ticket system</li>
              <li>• ticket_comments - Ticket discussions</li>
              <li>• admin_users - Admin management</li>
            </ul>
          </div>
        </div>

        {/* Results Panel */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">Creation Results</h2>
          <div className="bg-gray-900 text-green-400 p-4 rounded font-mono text-sm h-96 overflow-y-auto">
            {results.length === 0 ? (
              <div className="text-gray-500">Ready to create tables. Click the button to start.</div>
            ) : (
              results.map((result, index) => (
                <div key={index} className="mb-1">
                  {result}
                </div>
              ))
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
