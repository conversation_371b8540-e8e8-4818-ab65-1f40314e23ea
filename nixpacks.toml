[variables]
CI = 'true'
NODE_ENV = 'production'

[phases.setup]
nixPkgs = ['nodejs_18']
aptPkgs = ['curl', 'unzip']
cmds = ['curl -fsSL https://bun.sh/install | bash', 'export PATH="$HOME/.bun/bin:$PATH"']

[phases.install]
dependsOn = ['setup']
cmds = ['export PATH="$HOME/.bun/bin:$PATH" && bun install']

[phases.build]
dependsOn = ['install']
cmds = ['export PATH="$HOME/.bun/bin:$PATH" && bun run build']

[start]
cmd = 'export PATH="$HOME/.bun/bin:$PATH" && bun run start'
