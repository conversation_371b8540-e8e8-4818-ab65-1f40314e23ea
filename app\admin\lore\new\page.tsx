"use client"

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from "@/components/ui/breadcrumb"
import { 
  Save,
  ArrowLeft,
  AlertTriangle,
  CheckCircle,
  Loader2,
  BookOpen,
  Image
} from "lucide-react"
import { motion } from "framer-motion"
import { useAdminContent } from "@/hooks/use-stock"

export default function NewLoreChapterPage() {
  const router = useRouter()
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [nextChapterNumber, setNextChapterNumber] = useState(1)

  const { createLoreChapter, getAllLoreChapters } = useAdminContent()

  const [formData, setFormData] = useState({
    title: '',
    slug: '',
    content: '',
    excerpt: '',
    chapter_number: 1,
    featured_image: '',
    is_published: false
  })

  useEffect(() => {
    // Charger le prochain numéro de chapitre
    loadNextChapterNumber()
  }, [])

  const loadNextChapterNumber = async () => {
    try {
      const chapters = await getAllLoreChapters()
      const maxNumber = chapters.length > 0 ? Math.max(...chapters.map(c => c.chapter_number)) : 0
      const nextNumber = maxNumber + 1
      setNextChapterNumber(nextNumber)
      setFormData(prev => ({ ...prev, chapter_number: nextNumber }))
    } catch (err) {
      console.error('Erreur lors du chargement des chapitres:', err)
    }
  }

  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '')
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim()
  }

  const handleTitleChange = (title: string) => {
    setFormData(prev => ({
      ...prev,
      title,
      slug: prev.slug || generateSlug(title)
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    try {
      setSaving(true)
      setError(null)
      
      await createLoreChapter(formData)
      setSuccess('Chapitre créé avec succès')
      
      // Rediriger vers la page de gestion après 2 secondes
      setTimeout(() => {
        router.push('/admin/lore')
      }, 2000)
      
    } catch (err) {
      setError('Erreur lors de la création du chapitre')
    } finally {
      setSaving(false)
    }
  }

  return (
    <div>
      <header className="flex h-16 shrink-0 items-center gap-2 border-b border-base-300/50 bg-base-100/95 backdrop-blur px-4 shadow-sm">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/admin" className="text-base-content/60 hover:text-base-content">
                Administration
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink href="/admin/lore" className="text-base-content/60 hover:text-base-content">
                Lore
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage className="text-base-content font-medium">Nouveau Chapitre</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
        
        <div className="ml-auto">
          <Button variant="outline" size="sm" asChild>
            <a href="/admin/lore">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Retour
            </a>
          </Button>
        </div>
      </header>

      <main className="p-6 max-w-4xl mx-auto">
        {/* Messages */}
        {error && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center gap-2"
          >
            <AlertTriangle className="h-5 w-5 text-red-500" />
            <span className="text-red-700">{error}</span>
            <Button variant="ghost" size="sm" onClick={() => setError(null)} className="ml-auto">
              ×
            </Button>
          </motion.div>
        )}

        {success && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg flex items-center gap-2"
          >
            <CheckCircle className="h-5 w-5 text-green-500" />
            <span className="text-green-700">{success}</span>
          </motion.div>
        )}

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <Card className="bg-base-100/95 backdrop-blur border-primary/20">
            <CardHeader>
              <div className="flex items-center gap-3">
                <BookOpen className="h-6 w-6 text-primary" />
                <div>
                  <CardTitle className="text-2xl">Nouveau Chapitre de Lore</CardTitle>
                  <CardDescription>
                    Ajoutez un nouveau chapitre à l'histoire du Royaume des Tempêtes
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="chapter_number">Numéro du chapitre</Label>
                    <Input
                      id="chapter_number"
                      type="number"
                      value={formData.chapter_number}
                      onChange={(e) => setFormData(prev => ({ ...prev, chapter_number: parseInt(e.target.value) || 1 }))}
                      min="1"
                      required
                    />
                    <p className="text-sm text-base-content/60">
                      Numéro suggéré: {nextChapterNumber}
                    </p>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="featured_image">Image de couverture</Label>
                    <Input
                      id="featured_image"
                      value={formData.featured_image}
                      onChange={(e) => setFormData(prev => ({ ...prev, featured_image: e.target.value }))}
                      placeholder="/images/chapter1.jpg"
                    />
                    <p className="text-sm text-base-content/60">
                      URL de l'image de couverture (optionnel)
                    </p>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="title">Titre du chapitre</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => handleTitleChange(e.target.value)}
                    placeholder="Ex: La Fondation du Royaume"
                    required
                  />
                  <p className="text-sm text-base-content/60">
                    Le titre principal de ce chapitre
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="slug">Slug (URL)</Label>
                  <Input
                    id="slug"
                    value={formData.slug}
                    onChange={(e) => setFormData(prev => ({ ...prev, slug: e.target.value }))}
                    placeholder="fondation-royaume"
                    required
                  />
                  <p className="text-sm text-base-content/60">
                    Identifiant unique pour l'URL (généré automatiquement depuis le titre)
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="excerpt">Extrait (résumé)</Label>
                  <Textarea
                    id="excerpt"
                    value={formData.excerpt}
                    onChange={(e) => setFormData(prev => ({ ...prev, excerpt: e.target.value }))}
                    placeholder="Un court résumé de ce chapitre qui apparaîtra dans la liste..."
                    rows={3}
                  />
                  <p className="text-sm text-base-content/60">
                    Résumé court qui apparaît dans la liste des chapitres
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="content">Contenu du chapitre</Label>
                  <Textarea
                    id="content"
                    value={formData.content}
                    onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
                    placeholder="Il y a des siècles, dans les terres mystérieuses où les tempêtes magiques dansent éternellement..."
                    rows={12}
                    required
                  />
                  <p className="text-sm text-base-content/60">
                    Le contenu complet du chapitre (vous pouvez utiliser des paragraphes)
                  </p>
                </div>

                <Separator />

                <div className="flex items-center justify-between p-4 bg-base-200/50 rounded-lg">
                  <div className="space-y-1">
                    <Label htmlFor="published" className="text-base font-medium">
                      Publier ce chapitre
                    </Label>
                    <p className="text-sm text-base-content/60">
                      Le chapitre sera visible sur le site public
                    </p>
                  </div>
                  <Switch
                    id="published"
                    checked={formData.is_published}
                    onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_published: checked }))}
                  />
                </div>

                <Separator />

                <div className="flex gap-3 pt-4">
                  <Button type="submit" disabled={saving} className="flex-1">
                    {saving ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Création en cours...
                      </>
                    ) : (
                      <>
                        <Save className="h-4 w-4 mr-2" />
                        Créer le chapitre
                      </>
                    )}
                  </Button>
                  <Button type="button" variant="outline" asChild>
                    <a href="/admin/lore">
                      Annuler
                    </a>
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </motion.div>

        {/* Aide */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="mt-6"
        >
          <Card className="bg-purple-50/50 border-purple-200">
            <CardHeader>
              <CardTitle className="text-lg text-purple-800">📚 Conseils pour le Lore</CardTitle>
            </CardHeader>
            <CardContent className="text-purple-700 space-y-2">
              <p>• Numérotez vos chapitres dans l'ordre chronologique de l'histoire</p>
              <p>• L'extrait doit donner envie de lire le chapitre complet</p>
              <p>• Utilisez des images de couverture pour rendre vos chapitres plus attractifs</p>
              <p>• Vous pouvez créer le chapitre en brouillon et le publier plus tard</p>
              <p>• Pensez à la cohérence avec les chapitres existants</p>
            </CardContent>
          </Card>
        </motion.div>
      </main>
    </div>
  )
}
