"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import {
  Server,
  Activity,
  Users,
  Cpu,
  HardDrive,
  Wifi,
  Power,
  PowerOff,
  RefreshCw,
  Settings,
  Terminal,
  Database,
  Shield,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  BarChart3,
  TrendingUp,
  TrendingDown,
  Zap,
  Globe,
  MapPin,
  Monitor,
  MoreHorizontal,
  Play,
  Square,
  RotateCcw,
  Download,
  Upload,
  Eye,
  Edit3,
} from "lucide-react"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  AreaChart,
  Area,
} from "recharts"

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
}

const cardVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      type: "spring",
      stiffness: 300,
      damping: 24,
    },
  },
}

// Données mockup pour les serveurs
const mockServers = [
  {
    id: 1,
    name: "Serveur Principal",
    type: "Production",
    status: "En ligne",
    location: "Paris, France",
    ip: "*************",
    port: 25565,
    players: { current: 45, max: 100 },
    uptime: "15j 8h 23m",
    version: "1.20.4",
    cpu: 35,
    ram: { used: 6.2, total: 16 },
    disk: { used: 120, total: 500 },
    network: { in: 2.5, out: 1.8 },
    tps: 19.8,
    plugins: 24,
    lastRestart: "2024-01-15 14:30",
    autoRestart: true,
  },
  {
    id: 2,
    name: "Serveur Test",
    type: "Développement",
    status: "En ligne",
    location: "Londres, UK",
    ip: "*************",
    port: 25566,
    players: { current: 3, max: 20 },
    uptime: "2j 14h 45m",
    version: "1.21-snapshot",
    cpu: 12,
    ram: { used: 2.1, total: 8 },
    disk: { used: 45, total: 250 },
    network: { in: 0.3, out: 0.2 },
    tps: 20.0,
    plugins: 8,
    lastRestart: "2024-01-18 09:15",
    autoRestart: false,
  },
  {
    id: 3,
    name: "Serveur Events",
    type: "Événements",
    status: "Hors ligne",
    location: "Amsterdam, NL",
    ip: "*************",
    port: 25567,
    players: { current: 0, max: 50 },
    uptime: "0j 0h 0m",
    version: "1.20.4",
    cpu: 0,
    ram: { used: 0, total: 12 },
    disk: { used: 80, total: 300 },
    network: { in: 0, out: 0 },
    tps: 0,
    plugins: 15,
    lastRestart: "2024-01-17 22:00",
    autoRestart: true,
  },
  {
    id: 4,
    name: "Serveur Backup",
    type: "Sauvegarde",
    status: "Maintenance",
    location: "Francfort, DE",
    ip: "*************",
    port: 25568,
    players: { current: 0, max: 100 },
    uptime: "7j 2h 15m",
    version: "1.20.4",
    cpu: 5,
    ram: { used: 1.5, total: 16 },
    disk: { used: 200, total: 1000 },
    network: { in: 0.1, out: 0.1 },
    tps: 0,
    plugins: 20,
    lastRestart: "2024-01-12 03:00",
    autoRestart: true,
  },
]

// Données pour les graphiques
const performanceData = [
  { time: "00:00", cpu: 25, ram: 45, players: 12 },
  { time: "04:00", cpu: 15, ram: 42, players: 8 },
  { time: "08:00", cpu: 35, ram: 48, players: 25 },
  { time: "12:00", cpu: 45, ram: 55, players: 42 },
  { time: "16:00", cpu: 55, ram: 62, players: 38 },
  { time: "20:00", cpu: 65, ram: 68, players: 45 },
  { time: "24:00", cpu: 35, ram: 52, players: 28 },
]

const networkData = [
  { time: "00:00", in: 1.2, out: 0.8 },
  { time: "04:00", in: 0.5, out: 0.3 },
  { time: "08:00", in: 2.1, out: 1.5 },
  { time: "12:00", in: 3.2, out: 2.1 },
  { time: "16:00", in: 2.8, out: 1.9 },
  { time: "20:00", in: 3.5, out: 2.3 },
  { time: "24:00", in: 2.5, out: 1.8 },
]

export default function AdminServersPage() {
  const [selectedServer, setSelectedServer] = useState<typeof mockServers[0] | null>(null)
  const [isServerDialogOpen, setIsServerDialogOpen] = useState(false)
  const [actionDialogOpen, setActionDialogOpen] = useState(false)
  const [pendingAction, setPendingAction] = useState<"start" | "stop" | "restart" | null>(null)

  const getStatusColor = (status: string) => {
    switch (status) {
      case "En ligne":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
      case "Hors ligne":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
      case "Maintenance":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "En ligne":
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case "Hors ligne":
        return <XCircle className="h-4 w-4 text-red-500" />
      case "Maintenance":
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />
      default:
        return <XCircle className="h-4 w-4 text-gray-500" />
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case "Production":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
      case "Développement":
        return "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200"
      case "Événements":
        return "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200"
      case "Sauvegarde":
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"
    }
  }

  const handleServerAction = (server: typeof mockServers[0], action: "start" | "stop" | "restart") => {
    setSelectedServer(server)
    setPendingAction(action)
    setActionDialogOpen(true)
  }

  const handleServerDetails = (server: typeof mockServers[0]) => {
    setSelectedServer(server)
    setIsServerDialogOpen(true)
  }

  const executeAction = () => {
    if (selectedServer && pendingAction) {
      console.log(`Executing ${pendingAction} on server ${selectedServer.name}`)
      // Ici on exécuterait l'action réelle
      setActionDialogOpen(false)
      setPendingAction(null)
    }
  }

  const totalServers = mockServers.length
  const onlineServers = mockServers.filter(s => s.status === "En ligne").length
  const totalPlayers = mockServers.reduce((sum, s) => sum + s.players.current, 0)
  const maxPlayers = mockServers.reduce((sum, s) => sum + s.players.max, 0)
  const avgCpu = Math.round(mockServers.reduce((sum, s) => sum + s.cpu, 0) / totalServers)
  const avgRam = Math.round(mockServers.reduce((sum, s) => sum + (s.ram.used / s.ram.total * 100), 0) / totalServers)

  return (
    <>
      {/* Header */}
      <header className="flex h-16 shrink-0 items-center gap-2 border-b border-red-300/50 bg-gradient-to-r from-red-50 to-orange-50 dark:from-red-950 dark:to-orange-950 backdrop-blur supports-[backdrop-filter]:bg-red-50/80 px-4 shadow-sm relative z-10">
        <SidebarTrigger className="-ml-1 hover:bg-red-500/10 transition-colors" />
        <Separator orientation="vertical" className="mr-2 h-4 bg-red-300/50" />
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem className="hidden md:block">
              <BreadcrumbLink href="/admin" className="text-red-600/60 hover:text-red-600 transition-colors">
                Administration
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator className="hidden md:block text-red-600/30" />
            <BreadcrumbItem>
              <BreadcrumbPage className="text-red-700 font-medium flex items-center gap-2">
                <Server className="h-4 w-4" />
                Gestion des Serveurs
              </BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
        
        {/* Actions rapides */}
        <div className="ml-auto flex items-center gap-2">
          <Button variant="outline" size="sm" className="border-red-200 hover:bg-red-50">
            <RefreshCw className="h-4 w-4 mr-2" />
            Actualiser
          </Button>
          <Button variant="outline" size="sm" className="border-red-200 hover:bg-red-50">
            <Terminal className="h-4 w-4 mr-2" />
            Console
          </Button>
          <Button size="sm" className="bg-red-500 hover:bg-red-600">
            <Server className="h-4 w-4 mr-2" />
            Nouveau serveur
          </Button>
        </div>
      </header>

      <div className="flex-1 space-y-6 p-6 min-h-screen relative overflow-hidden">
        {/* Statistiques globales */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="relative z-10"
        >
          <motion.div variants={cardVariants} className="grid gap-4 md:grid-cols-2 lg:grid-cols-6 mb-8">
            <Card className="border-blue-200 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-blue-700">Serveurs</CardTitle>
                <Server className="h-4 w-4 text-blue-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-800">{totalServers}</div>
                <p className="text-xs text-blue-600">{onlineServers} en ligne</p>
              </CardContent>
            </Card>

            <Card className="border-green-200 bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950 dark:to-green-900">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-green-700">Joueurs</CardTitle>
                <Users className="h-4 w-4 text-green-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-800">{totalPlayers}</div>
                <p className="text-xs text-green-600">/ {maxPlayers} max</p>
              </CardContent>
            </Card>

            <Card className="border-purple-200 bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950 dark:to-purple-900">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-purple-700">CPU Moyen</CardTitle>
                <Cpu className="h-4 w-4 text-purple-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-purple-800">{avgCpu}%</div>
                <Progress value={avgCpu} className="mt-2" />
              </CardContent>
            </Card>

            <Card className="border-orange-200 bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-950 dark:to-orange-900">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-orange-700">RAM Moyenne</CardTitle>
                <Database className="h-4 w-4 text-orange-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-orange-800">{avgRam}%</div>
                <Progress value={avgRam} className="mt-2" />
              </CardContent>
            </Card>

            <Card className="border-yellow-200 bg-gradient-to-br from-yellow-50 to-yellow-100 dark:from-yellow-950 dark:to-yellow-900">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-yellow-700">Uptime</CardTitle>
                <Clock className="h-4 w-4 text-yellow-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-yellow-800">99.8%</div>
                <p className="text-xs text-yellow-600">30 derniers jours</p>
              </CardContent>
            </Card>

            <Card className="border-red-200 bg-gradient-to-br from-red-50 to-red-100 dark:from-red-950 dark:to-red-900">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-red-700">Alertes</CardTitle>
                <AlertTriangle className="h-4 w-4 text-red-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-800">2</div>
                <p className="text-xs text-red-600">Nécessitent attention</p>
              </CardContent>
            </Card>
          </motion.div>

          {/* Graphiques de performance */}
          <motion.div variants={cardVariants} className="grid gap-6 md:grid-cols-2 mb-8">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Performance Système
                </CardTitle>
                <CardDescription>CPU, RAM et nombre de joueurs sur 24h</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={performanceData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="time" />
                    <YAxis />
                    <Tooltip />
                    <Line type="monotone" dataKey="cpu" stroke="#8884d8" name="CPU %" />
                    <Line type="monotone" dataKey="ram" stroke="#82ca9d" name="RAM %" />
                    <Line type="monotone" dataKey="players" stroke="#ffc658" name="Joueurs" />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Wifi className="h-5 w-5" />
                  Trafic Réseau
                </CardTitle>
                <CardDescription>Bande passante entrante et sortante (MB/s)</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={networkData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="time" />
                    <YAxis />
                    <Tooltip />
                    <Area type="monotone" dataKey="in" stackId="1" stroke="#8884d8" fill="#8884d8" name="Entrant" />
                    <Area type="monotone" dataKey="out" stackId="1" stroke="#82ca9d" fill="#82ca9d" name="Sortant" />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </motion.div>

          {/* Liste des serveurs */}
          <motion.div variants={cardVariants}>
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <Server className="h-5 w-5" />
                      Liste des Serveurs
                    </CardTitle>
                    <CardDescription>
                      Gestion et monitoring de tous les serveurs
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Serveur</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Statut</TableHead>
                      <TableHead>Joueurs</TableHead>
                      <TableHead>Performance</TableHead>
                      <TableHead>Uptime</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {mockServers.map((server) => (
                      <TableRow key={server.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium flex items-center gap-2">
                              {getStatusIcon(server.status)}
                              {server.name}
                            </div>
                            <div className="text-sm text-muted-foreground flex items-center gap-1">
                              <MapPin className="h-3 w-3" />
                              {server.location}
                            </div>
                            <div className="text-xs text-muted-foreground font-mono">
                              {server.ip}:{server.port}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge className={getTypeColor(server.type)}>
                            {server.type}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge className={getStatusColor(server.status)}>
                            {server.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Users className="h-4 w-4 text-muted-foreground" />
                            <span className="font-medium">{server.players.current}</span>
                            <span className="text-muted-foreground">/ {server.players.max}</span>
                          </div>
                          <Progress 
                            value={(server.players.current / server.players.max) * 100} 
                            className="mt-1 h-1" 
                          />
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            <div className="flex items-center gap-2 text-sm">
                              <Cpu className="h-3 w-3" />
                              <span>{server.cpu}%</span>
                            </div>
                            <div className="flex items-center gap-2 text-sm">
                              <Database className="h-3 w-3" />
                              <span>{Math.round((server.ram.used / server.ram.total) * 100)}%</span>
                            </div>
                            <div className="flex items-center gap-2 text-sm">
                              <Zap className="h-3 w-3" />
                              <span>{server.tps} TPS</span>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            <div className="font-medium">{server.uptime}</div>
                            <div className="text-muted-foreground text-xs">
                              Redémarré: {server.lastRestart}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem onClick={() => handleServerDetails(server)}>
                                <Eye className="h-4 w-4 mr-2" />
                                Voir détails
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <Edit3 className="h-4 w-4 mr-2" />
                                Configurer
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <Terminal className="h-4 w-4 mr-2" />
                                Console
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              {server.status === "Hors ligne" ? (
                                <DropdownMenuItem 
                                  onClick={() => handleServerAction(server, "start")}
                                  className="text-green-600"
                                >
                                  <Play className="h-4 w-4 mr-2" />
                                  Démarrer
                                </DropdownMenuItem>
                              ) : (
                                <DropdownMenuItem 
                                  onClick={() => handleServerAction(server, "stop")}
                                  className="text-red-600"
                                >
                                  <Square className="h-4 w-4 mr-2" />
                                  Arrêter
                                </DropdownMenuItem>
                              )}
                              <DropdownMenuItem onClick={() => handleServerAction(server, "restart")}>
                                <RotateCcw className="h-4 w-4 mr-2" />
                                Redémarrer
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </motion.div>
        </motion.div>

        {/* Dialog détails serveur */}
        <Dialog open={isServerDialogOpen} onOpenChange={setIsServerDialogOpen}>
          <DialogContent className="max-w-4xl">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <Server className="h-5 w-5" />
                Détails du serveur - {selectedServer?.name}
              </DialogTitle>
              <DialogDescription>
                Informations complètes et monitoring en temps réel
              </DialogDescription>
            </DialogHeader>
            
            {selectedServer && (
              <Tabs defaultValue="overview" className="w-full">
                <TabsList className="grid w-full grid-cols-4">
                  <TabsTrigger value="overview">Vue d'ensemble</TabsTrigger>
                  <TabsTrigger value="performance">Performance</TabsTrigger>
                  <TabsTrigger value="players">Joueurs</TabsTrigger>
                  <TabsTrigger value="config">Configuration</TabsTrigger>
                </TabsList>
                
                <TabsContent value="overview" className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-lg">Informations générales</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-3">
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Nom:</span>
                          <span className="font-medium">{selectedServer.name}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Type:</span>
                          <Badge className={getTypeColor(selectedServer.type)}>{selectedServer.type}</Badge>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Statut:</span>
                          <Badge className={getStatusColor(selectedServer.status)}>{selectedServer.status}</Badge>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Version:</span>
                          <span className="font-medium">{selectedServer.version}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Localisation:</span>
                          <span className="font-medium">{selectedServer.location}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Adresse:</span>
                          <span className="font-mono text-sm">{selectedServer.ip}:{selectedServer.port}</span>
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader>
                        <CardTitle className="text-lg">Statistiques</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-3">
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Uptime:</span>
                          <span className="font-medium">{selectedServer.uptime}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Joueurs:</span>
                          <span className="font-medium">{selectedServer.players.current} / {selectedServer.players.max}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">TPS:</span>
                          <span className="font-medium">{selectedServer.tps}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Plugins:</span>
                          <span className="font-medium">{selectedServer.plugins}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Dernier redémarrage:</span>
                          <span className="font-medium text-sm">{selectedServer.lastRestart}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Auto-restart:</span>
                          <Badge variant={selectedServer.autoRestart ? "default" : "secondary"}>
                            {selectedServer.autoRestart ? "Activé" : "Désactivé"}
                          </Badge>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </TabsContent>
                
                <TabsContent value="performance" className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <Cpu className="h-4 w-4" />
                          Processeur
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-3xl font-bold mb-2">{selectedServer.cpu}%</div>
                        <Progress value={selectedServer.cpu} className="mb-2" />
                        <p className="text-sm text-muted-foreground">Utilisation actuelle</p>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <Database className="h-4 w-4" />
                          Mémoire RAM
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-3xl font-bold mb-2">
                          {selectedServer.ram.used}GB / {selectedServer.ram.total}GB
                        </div>
                        <Progress value={(selectedServer.ram.used / selectedServer.ram.total) * 100} className="mb-2" />
                        <p className="text-sm text-muted-foreground">
                          {Math.round((selectedServer.ram.used / selectedServer.ram.total) * 100)}% utilisé
                        </p>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <HardDrive className="h-4 w-4" />
                          Stockage
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-3xl font-bold mb-2">
                          {selectedServer.disk.used}GB / {selectedServer.disk.total}GB
                        </div>
                        <Progress value={(selectedServer.disk.used / selectedServer.disk.total) * 100} className="mb-2" />
                        <p className="text-sm text-muted-foreground">
                          {Math.round((selectedServer.disk.used / selectedServer.disk.total) * 100)}% utilisé
                        </p>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <Wifi className="h-4 w-4" />
                          Réseau
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span className="text-sm text-muted-foreground">Entrant:</span>
                            <span className="font-medium">{selectedServer.network.in} MB/s</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-muted-foreground">Sortant:</span>
                            <span className="font-medium">{selectedServer.network.out} MB/s</span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </TabsContent>
                
                <TabsContent value="players" className="space-y-4">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Users className="h-4 w-4" />
                        Joueurs connectés
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-center py-8">
                        <Users className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                        <p className="text-muted-foreground">Liste des joueurs connectés</p>
                        <p className="text-sm text-muted-foreground mt-2">
                          {selectedServer.players.current} joueur(s) actuellement en ligne
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
                
                <TabsContent value="config" className="space-y-4">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Settings className="h-4 w-4" />
                        Configuration du serveur
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-center py-8">
                        <Settings className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                        <p className="text-muted-foreground">Paramètres de configuration</p>
                        <p className="text-sm text-muted-foreground mt-2">
                          Gestion des propriétés et plugins du serveur
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
              </Tabs>
            )}
          </DialogContent>
        </Dialog>

        {/* Dialog confirmation d'action */}
        <Dialog open={actionDialogOpen} onOpenChange={setActionDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5 text-yellow-500" />
                Confirmer l'action
              </DialogTitle>
              <DialogDescription>
                Êtes-vous sûr de vouloir {pendingAction === "start" && "démarrer"}
                {pendingAction === "stop" && "arrêter"}
                {pendingAction === "restart" && "redémarrer"}
                {" "}{selectedServer?.name} ?
              </DialogDescription>
            </DialogHeader>
            
            <div className="py-4">
              <p className="text-sm text-muted-foreground">
                Cette action {pendingAction === "restart" && "redémarrera le serveur et déconnectera tous les joueurs."}
                {pendingAction === "stop" && "arrêtera le serveur et déconnectera tous les joueurs."}
                {pendingAction === "start" && "démarrera le serveur."}
              </p>
            </div>
            
            <DialogFooter>
              <Button variant="outline" onClick={() => setActionDialogOpen(false)}>
                Annuler
              </Button>
              <Button 
                variant={pendingAction === "stop" ? "destructive" : "default"}
                onClick={executeAction}
              >
                Confirmer
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </>
  )
}