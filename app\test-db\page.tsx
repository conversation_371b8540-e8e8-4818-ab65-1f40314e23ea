"use client"

import { useState, useEffect } from 'react'
import { supabase } from '@/lib/supabase'

export default function TestDatabasePage() {
  const [connectionStatus, setConnectionStatus] = useState<'testing' | 'connected' | 'error'>('testing')
  const [error, setError] = useState<string | null>(null)
  const [tables, setTables] = useState<string[]>([])
  const [supabaseConfig, setSupabaseConfig] = useState<any>(null)

  useEffect(() => {
    testConnection()
    checkConfig()
  }, [])

  const checkConfig = () => {
    setSupabaseConfig({
      url: process.env.NEXT_PUBLIC_SUPABASE_URL,
      hasAnonKey: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      anonKeyLength: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY?.length || 0
    })
  }

  const testConnection = async () => {
    try {
      setConnectionStatus('testing')
      setError(null)

      // Try a simpler test first - just check if we can connect
      const { error: connectionError } = await supabase
        .from('_realtime')
        .select('*')
        .limit(1)

      if (connectionError) {
        // Try an even simpler approach
        const { error: authError } = await supabase.auth.getSession()

        if (authError) {
          throw new Error(`Connection failed: ${authError.message}`)
        }

        // If auth works, the connection is probably OK
        setConnectionStatus('connected')
        setTables(['Connection verified via auth'])
        return
      }

      setTables(['_realtime'])
      setConnectionStatus('connected')
    } catch (err) {
      setConnectionStatus('error')
      setError(err instanceof Error ? err.message : 'Unknown error')
    }
  }

  const createTablesDirectly = async () => {
    try {
      setConnectionStatus('testing')
      setError(null)

      // Try to create tables using direct Supabase client calls
      const tables = [
        {
          name: 'ticket_categories',
          create: () => supabase.from('ticket_categories').select('*').limit(1)
        },
        {
          name: 'reglement_sections',
          create: () => supabase.from('reglement_sections').select('*').limit(1)
        },
        {
          name: 'lore_chapters',
          create: () => supabase.from('lore_chapters').select('*').limit(1)
        }
      ]

      const results = []
      for (const table of tables) {
        try {
          await table.create()
          results.push(`✅ ${table.name} - exists or accessible`)
        } catch (err) {
          results.push(`❌ ${table.name} - ${err instanceof Error ? err.message : 'error'}`)
        }
      }

      setTables(results)
      setConnectionStatus('connected')
    } catch (err) {
      setConnectionStatus('error')
      setError(err instanceof Error ? err.message : 'Failed to check tables')
    }
  }

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <h1 className="text-3xl font-bold mb-6">Database Connection Test</h1>
      
      <div className="space-y-6">
        {/* Connection Status */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">Connection Status</h2>
          <div className="flex items-center space-x-3">
            {connectionStatus === 'testing' && (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
                <span className="text-blue-600">Testing connection...</span>
              </>
            )}
            {connectionStatus === 'connected' && (
              <>
                <div className="h-5 w-5 bg-green-500 rounded-full"></div>
                <span className="text-green-600">Connected successfully!</span>
              </>
            )}
            {connectionStatus === 'error' && (
              <>
                <div className="h-5 w-5 bg-red-500 rounded-full"></div>
                <span className="text-red-600">Connection failed</span>
              </>
            )}
          </div>
          
          {error && (
            <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded">
              <p className="text-red-700 font-medium">Error:</p>
              <p className="text-red-600 text-sm mt-1">{error}</p>
            </div>
          )}
        </div>

        {/* Configuration */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">Supabase Configuration</h2>
          {supabaseConfig && (
            <div className="space-y-2 text-sm">
              <div><strong>URL:</strong> {supabaseConfig.url}</div>
              <div><strong>Anon Key:</strong> {supabaseConfig.hasAnonKey ? `✅ Present (${supabaseConfig.anonKeyLength} chars)` : '❌ Missing'}</div>
            </div>
          )}
        </div>

        {/* Current Tables */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">Database Status</h2>
          {tables.length > 0 ? (
            <div className="space-y-2">
              {tables.map((table, index) => (
                <div key={index} className="bg-gray-100 px-3 py-2 rounded text-sm">
                  {table}
                </div>
              ))}
            </div>
          ) : (
            <p className="text-gray-500">No status information available</p>
          )}
        </div>

        {/* Actions */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">Actions</h2>
          <div className="space-x-4">
            <button
              onClick={testConnection}
              className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
              disabled={connectionStatus === 'testing'}
            >
              Test Connection
            </button>
            <button
              onClick={initializeDatabase}
              className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
              disabled={connectionStatus === 'testing'}
            >
              Initialize Database
            </button>
          </div>
        </div>

        {/* Instructions */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4 text-blue-800">Setup Instructions</h2>
          <div className="space-y-3 text-blue-700">
            <p>1. Make sure your Supabase credentials are correctly set in .env.local</p>
            <p>2. If the connection test passes, run the database initialization script:</p>
            <code className="block bg-blue-100 p-2 rounded mt-2 text-sm">
              bun run db:init
            </code>
            <p>3. This will create all the necessary tables for the content management system</p>
          </div>
        </div>
      </div>
    </div>
  )
}
