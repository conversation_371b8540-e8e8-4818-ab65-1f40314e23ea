"use client"

import { useState, useEffect } from 'react'
import { supabase } from '@/lib/supabase'

export default function TestDatabasePage() {
  const [connectionStatus, setConnectionStatus] = useState<'testing' | 'connected' | 'error'>('testing')
  const [error, setError] = useState<string | null>(null)
  const [tables, setTables] = useState<string[]>([])

  useEffect(() => {
    testConnection()
  }, [])

  const testConnection = async () => {
    try {
      setConnectionStatus('testing')
      setError(null)

      // Test basic connection
      const { data, error: connectionError } = await supabase
        .from('information_schema.tables')
        .select('table_name')
        .eq('table_schema', 'public')

      if (connectionError) {
        throw connectionError
      }

      setTables(data?.map(t => t.table_name) || [])
      setConnectionStatus('connected')
    } catch (err) {
      setConnectionStatus('error')
      setError(err instanceof Error ? err.message : 'Unknown error')
    }
  }

  const initializeDatabase = async () => {
    try {
      setConnectionStatus('testing')
      setError(null)

      // Try to create a simple test table to verify permissions
      const { error } = await supabase.rpc('exec_sql', {
        sql: 'CREATE TABLE IF NOT EXISTS test_connection (id SERIAL PRIMARY KEY, created_at TIMESTAMP DEFAULT NOW());'
      })

      if (error) {
        throw error
      }

      await testConnection()
    } catch (err) {
      setConnectionStatus('error')
      setError(err instanceof Error ? err.message : 'Failed to initialize database')
    }
  }

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <h1 className="text-3xl font-bold mb-6">Database Connection Test</h1>
      
      <div className="space-y-6">
        {/* Connection Status */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">Connection Status</h2>
          <div className="flex items-center space-x-3">
            {connectionStatus === 'testing' && (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
                <span className="text-blue-600">Testing connection...</span>
              </>
            )}
            {connectionStatus === 'connected' && (
              <>
                <div className="h-5 w-5 bg-green-500 rounded-full"></div>
                <span className="text-green-600">Connected successfully!</span>
              </>
            )}
            {connectionStatus === 'error' && (
              <>
                <div className="h-5 w-5 bg-red-500 rounded-full"></div>
                <span className="text-red-600">Connection failed</span>
              </>
            )}
          </div>
          
          {error && (
            <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded">
              <p className="text-red-700 font-medium">Error:</p>
              <p className="text-red-600 text-sm mt-1">{error}</p>
            </div>
          )}
        </div>

        {/* Current Tables */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">Current Database Tables</h2>
          {tables.length > 0 ? (
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
              {tables.map((table) => (
                <div key={table} className="bg-gray-100 px-3 py-2 rounded text-sm">
                  {table}
                </div>
              ))}
            </div>
          ) : (
            <p className="text-gray-500">No tables found or connection failed</p>
          )}
        </div>

        {/* Actions */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">Actions</h2>
          <div className="space-x-4">
            <button
              onClick={testConnection}
              className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
              disabled={connectionStatus === 'testing'}
            >
              Test Connection
            </button>
            <button
              onClick={initializeDatabase}
              className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
              disabled={connectionStatus === 'testing'}
            >
              Initialize Database
            </button>
          </div>
        </div>

        {/* Instructions */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4 text-blue-800">Setup Instructions</h2>
          <div className="space-y-3 text-blue-700">
            <p>1. Make sure your Supabase credentials are correctly set in .env.local</p>
            <p>2. If the connection test passes, run the database initialization script:</p>
            <code className="block bg-blue-100 p-2 rounded mt-2 text-sm">
              bun run db:init
            </code>
            <p>3. This will create all the necessary tables for the content management system</p>
          </div>
        </div>
      </div>
    </div>
  )
}
