import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

if (!supabaseUrl || !supabaseServiceKey) {
  throw new Error('Missing Supabase environment variables')
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

export async function POST(request: NextRequest) {
  try {
    const { dataType } = await request.json()
    
    if (dataType === 'ticket_categories' || dataType === 'all') {
      try {
        const { error } = await supabase.from('ticket_categories').upsert([
          { 
            name: 'Bug Report', 
            description: 'Signalement de bugs et problèmes techniques', 
            color: '#FF6B6B', 
            icon: 'Bug',
            is_active: true
          },
          { 
            name: 'Feature Request', 
            description: 'Demandes de nouvelles fonctionnalités', 
            color: '#4ECDC4', 
            icon: 'Lightbulb',
            is_active: true
          },
          { 
            name: 'Support', 
            description: 'Demandes d\'aide et support général', 
            color: '#45B7D1', 
            icon: 'HelpCircle',
            is_active: true
          },
          { 
            name: 'Modération', 
            description: 'Questions liées à la modération', 
            color: '#96CEB4', 
            icon: 'Shield',
            is_active: true
          },
          { 
            name: 'Événement', 
            description: 'Organisation et gestion d\'événements', 
            color: '#FFEAA7', 
            icon: 'Calendar',
            is_active: true
          },
          { 
            name: 'Autre', 
            description: 'Autres demandes non catégorisées', 
            color: '#A0A0A0', 
            icon: 'MessageSquare',
            is_active: true
          }
        ], { onConflict: 'name' })
        
        if (error) {
          console.error('Error inserting ticket categories:', error)
          return NextResponse.json({
            success: false,
            error: `Failed to insert ticket categories: ${error.message}`
          })
        }
      } catch (err) {
        console.error('Error with ticket categories:', err)
        return NextResponse.json({
          success: false,
          error: `Error inserting ticket categories: ${err instanceof Error ? err.message : 'Unknown error'}`
        })
      }
    }
    
    if (dataType === 'reglement_sections' || dataType === 'all') {
      try {
        const { error } = await supabase.from('reglement_sections').upsert([
          { 
            title: 'Règles Générales', 
            slug: 'regles-generales', 
            description: 'Règles de base du serveur Discord', 
            order_index: 1, 
            is_published: true 
          },
          { 
            title: 'Règles de Chat', 
            slug: 'regles-chat', 
            description: 'Règles spécifiques aux canaux de discussion', 
            order_index: 2, 
            is_published: true 
          },
          { 
            title: 'Règles de Jeu', 
            slug: 'regles-jeu', 
            description: 'Règles pour les activités de jeu', 
            order_index: 3, 
            is_published: true 
          },
          { 
            title: 'Sanctions', 
            slug: 'sanctions', 
            description: 'Système de sanctions et procédures', 
            order_index: 4, 
            is_published: true 
          }
        ], { onConflict: 'slug' })
        
        if (error) {
          console.error('Error inserting reglement sections:', error)
          return NextResponse.json({
            success: false,
            error: `Failed to insert règlement sections: ${error.message}`
          })
        }
      } catch (err) {
        console.error('Error with reglement sections:', err)
        return NextResponse.json({
          success: false,
          error: `Error inserting règlement sections: ${err instanceof Error ? err.message : 'Unknown error'}`
        })
      }
    }
    
    if (dataType === 'lore_chapters' || dataType === 'all') {
      try {
        const { error } = await supabase.from('lore_chapters').upsert([
          {
            title: 'La Fondation du Royaume',
            slug: 'fondation-royaume',
            content: 'Il y a des siècles, dans les terres mystérieuses où les tempêtes magiques dansent éternellement, un royaume fut fondé par des héros légendaires. Ces terres, battues par des vents surnaturels et illuminées par des éclairs perpétuels, abritaient des secrets anciens et des pouvoirs oubliés.\n\nLes premiers habitants de ces terres étaient des nomades, des aventuriers et des mages qui cherchaient à comprendre la nature des tempêtes magiques. Ils découvrirent que ces phénomènes n\'étaient pas naturels, mais le résultat d\'une ancienne magie qui imprégnait la terre elle-même.\n\nC\'est ainsi que naquit le Royaume des Tempêtes, un lieu où la magie et la nature se mélangent dans une danse éternelle de pouvoir et de mystère.',
            excerpt: 'L\'histoire de la création du Royaume des Tempêtes et de ses premiers dirigeants.',
            chapter_number: 1,
            is_published: true,
            featured_image: '/images/chapter1.jpg'
          },
          {
            title: 'Les Grandes Tempêtes',
            slug: 'grandes-tempetes',
            content: 'Les tempêtes qui donnent son nom au royaume ne sont pas ordinaires. Elles sont imprégnées d\'une magie ancienne qui remonte aux premiers âges du monde. Ces tempêtes magiques apparaissent de manière cyclique, apportant avec elles des changements profonds dans le royaume.\n\nChaque tempête a sa propre personnalité et ses propres effets. Certaines apportent la guérison et la croissance, tandis que d\'autres peuvent transformer le paysage ou révéler des secrets cachés. Les habitants du royaume ont appris à lire les signes et à se préparer à ces événements extraordinaires.\n\nLes mages du royaume étudient ces phénomènes depuis des générations, cherchant à comprendre leur origine et leur but. Leurs recherches ont révélé que les tempêtes sont liées à un réseau complexe de lignes de force magiques qui traversent tout le royaume.',
            excerpt: 'Découvrez les mystères des tempêtes magiques qui façonnent le royaume.',
            chapter_number: 2,
            is_published: true,
            featured_image: '/images/chapter2.jpg'
          }
        ], { onConflict: 'slug' })
        
        if (error) {
          console.error('Error inserting lore chapters:', error)
          return NextResponse.json({
            success: false,
            error: `Failed to insert lore chapters: ${error.message}`
          })
        }
      } catch (err) {
        console.error('Error with lore chapters:', err)
        return NextResponse.json({
          success: false,
          error: `Error inserting lore chapters: ${err instanceof Error ? err.message : 'Unknown error'}`
        })
      }
    }
    
    return NextResponse.json({
      success: true,
      message: `Sample data for ${dataType} inserted successfully`
    })
    
  } catch (error) {
    console.error('Error inserting sample data:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
