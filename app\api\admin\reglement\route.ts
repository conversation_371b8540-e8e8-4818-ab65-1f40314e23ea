import { NextRequest, NextResponse } from 'next/server'
import { adminService } from '@/lib/supabase'

export async function GET() {
  try {
    const sections = await adminService.getAllReglementSections()
    return NextResponse.json({ success: true, data: sections })
  } catch (error) {
    console.error('Error fetching règlement sections:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch règlement sections' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const section = await adminService.createReglementSection(body)
    return NextResponse.json({ success: true, data: section })
  } catch (error) {
    console.error('Error creating règlement section:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to create règlement section' },
      { status: 500 }
    )
  }
}
