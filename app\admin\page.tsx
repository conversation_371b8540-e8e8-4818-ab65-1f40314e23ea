"use client"

import { useEffect, useState } from 'react'
import { motion } from "framer-motion"
import {
  Users,
  FileText,
  Settings,
  Shield,
  Crown,
  Activity,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Clock,
  Edit3,
  Save,
  Eye,
  BarChart3,
  MessageSquare,
  Bell,
  Search,
  Filter,
  Plus,
  Download,
  Upload,
  BookOpen,
} from "lucide-react"
// Removed SidebarTrigger import - admin layout is independent
import { Separator } from "@/components/ui/separator"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Input } from "@/components/ui/input"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { useAdminContent } from "@/hooks/use-stock"

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
}

const cardVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      type: "spring",
      stiffness: 300,
      damping: 24,
    },
  },
}

// Données mockup pour l'administration
const mockStats = {
  totalUsers: 1247,
  activeUsers: 892,
  pendingTickets: 23,
  contentPages: 45,
  serverUptime: 99.8,
  dailyVisits: 3421,
}

const mockRecentActions = [
  {
    id: 1,
    user: "Admin_Sarah",
    action: "Modification de la page Lore - Chapitre 3",
    timestamp: "Il y a 5 minutes",
    type: "edit",
  },
  {
    id: 2,
    user: "Modo_Alex",
    action: "Résolution du ticket #1247",
    timestamp: "Il y a 12 minutes",
    type: "ticket",
  },
  {
    id: 3,
    user: "Admin_Sarah",
    action: "Ajout d'une nouvelle règle PvP",
    timestamp: "Il y a 1 heure",
    type: "rule",
  },
  {
    id: 4,
    user: "Modo_Jordan",
    action: "Modération utilisateur: TempBan_User123",
    timestamp: "Il y a 2 heures",
    type: "moderation",
  },
]

const mockUsers = [
  {
    id: 1,
    username: "DragonSlayer_42",
    email: "<EMAIL>",
    role: "Utilisateur",
    status: "En ligne",
    joinDate: "2024-01-15",
    lastActive: "Il y a 2 minutes",
  },
  {
    id: 2,
    username: "MageSupreme",
    email: "<EMAIL>",
    role: "Modérateur",
    status: "En ligne",
    joinDate: "2023-11-20",
    lastActive: "Il y a 5 minutes",
  },
  {
    id: 3,
    username: "KnightOfStorms",
    email: "<EMAIL>",
    role: "Utilisateur",
    status: "Hors ligne",
    joinDate: "2024-02-03",
    lastActive: "Il y a 1 heure",
  },
]

const mockTickets = [
  {
    id: 1247,
    title: "Bug d'affichage sur la page Lore",
    user: "TestUser_99",
    priority: "Haute",
    status: "Ouvert",
    category: "Bug",
    created: "2024-01-20 14:30",
  },
  {
    id: 1246,
    title: "Demande d'ajout de fonctionnalité",
    user: "FeatureRequester",
    priority: "Normale",
    status: "En cours",
    category: "Feature",
    created: "2024-01-20 10:15",
  },
  {
    id: 1245,
    title: "Problème de connexion Discord",
    user: "DiscordUser123",
    priority: "Critique",
    status: "Résolu",
    category: "Support",
    created: "2024-01-19 16:45",
  },
]

export default function AdminPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedTab, setSelectedTab] = useState("overview")
  const [stats, setStats] = useState<any>(null)
  const { getDashboardStats, loading } = useAdminContent()

  useEffect(() => {
    loadStats()
  }, [])

  const loadStats = async () => {
    const data = await getDashboardStats()
    setStats(data)
  }

  const getActionIcon = (type: string) => {
    switch (type) {
      case "edit":
        return <Edit3 className="h-4 w-4 text-blue-500" />
      case "ticket":
        return <MessageSquare className="h-4 w-4 text-green-500" />
      case "rule":
        return <FileText className="h-4 w-4 text-purple-500" />
      case "moderation":
        return <Shield className="h-4 w-4 text-red-500" />
      default:
        return <Activity className="h-4 w-4 text-gray-500" />
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "Critique":
        return "bg-red-500"
      case "Haute":
        return "bg-orange-500"
      case "Normale":
        return "bg-blue-500"
      case "Basse":
        return "bg-gray-500"
      default:
        return "bg-gray-500"
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Ouvert":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
      case "En cours":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"
      case "Résolu":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"
    }
  }

  return (
    <>
      {/* Header avec navigation admin */}
      <header className="flex h-16 shrink-0 items-center gap-2 border-b bg-card backdrop-blur supports-[backdrop-filter]:bg-card/80 px-4 shadow-sm relative z-10">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem className="hidden md:block">
              <BreadcrumbLink href="/" className="text-muted-foreground hover:text-foreground transition-colors">
                Dashboard
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator className="hidden md:block text-muted-foreground/50" />
            <BreadcrumbItem>
              <BreadcrumbPage className="text-foreground font-medium flex items-center gap-2">
                <Crown className="h-4 w-4" />
                Administration
              </BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
        
        {/* Actions rapides admin */}
        <div className="ml-auto flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Bell className="h-4 w-4 mr-2" />
            Notifications
            <Badge variant="destructive" className="ml-2">3</Badge>
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </header>

      <div className="flex-1 space-y-6 px-6 py-4 relative overflow-hidden w-full max-w-none">

        
        {/* Welcome Section Admin */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="relative z-10 w-full max-w-none"
        >
          <motion.div variants={cardVariants} className="mb-8">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-4xl font-bold bg-gradient-to-r from-red-600 to-orange-600 bg-clip-text text-transparent">
                  🏰 Panneau d'Administration
                </h1>
                <p className="text-lg text-muted-foreground mt-2">
                  Gérez votre royaume avec puissance et sagesse
                </p>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="outline" className="border-green-500 text-green-700">
                  <CheckCircle className="h-3 w-3 mr-1" />
                  Système opérationnel
                </Badge>
                <Badge variant="outline" className="border-blue-500 text-blue-700">
                  <Clock className="h-3 w-3 mr-1" />
                  Uptime: {mockStats.serverUptime}%
                </Badge>
              </div>
            </div>
          </motion.div>

          {/* Statistiques principales */}
          <motion.div variants={cardVariants} className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-8">
            <Card className="bg-base-100/95 backdrop-blur border-blue-500/20 shadow-lg hover:shadow-xl transition-all duration-300">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-base-content">Utilisateurs Total</CardTitle>
                <Users className="h-4 w-4 text-blue-400" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-base-content">{mockStats.totalUsers.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",")}</div>
                <p className="text-xs text-base-content/70 mt-1">
                  <TrendingUp className="h-3 w-3 inline mr-1" />
                  +12% ce mois
                </p>
              </CardContent>
            </Card>

            <Card className="bg-base-100/95 backdrop-blur border-green-500/20 shadow-lg hover:shadow-xl transition-all duration-300">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-base-content">Utilisateurs Actifs</CardTitle>
                <Activity className="h-4 w-4 text-green-400" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-base-content">{mockStats.activeUsers.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",")}</div>
                <p className="text-xs text-base-content/70 mt-1">
                  <TrendingUp className="h-3 w-3 inline mr-1" />
                  +8% cette semaine
                </p>
              </CardContent>
            </Card>

            <Card className="bg-base-100/95 backdrop-blur border-red-500/20 shadow-lg hover:shadow-xl transition-all duration-300">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-base-content">Tickets en Attente</CardTitle>
                <MessageSquare className="h-4 w-4 text-red-400" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-base-content">{mockStats.pendingTickets}</div>
                <p className="text-xs text-base-content/70 mt-1">
                  <AlertTriangle className="h-3 w-3 inline mr-1" />
                  3 critiques
                </p>
              </CardContent>
            </Card>

            <Card className="bg-base-100/95 backdrop-blur border-purple-500/20 shadow-lg hover:shadow-xl transition-all duration-300">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-base-content">Pages de Contenu</CardTitle>
                <FileText className="h-4 w-4 text-purple-400" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-base-content">{mockStats.contentPages}</div>
                <p className="text-xs text-base-content/70 mt-1">
                  <Edit3 className="h-3 w-3 inline mr-1" />
                  5 modifiées aujourd'hui
                </p>
              </CardContent>
            </Card>
          </motion.div>

          {/* Interface à onglets */}
          <motion.div variants={cardVariants}>
            <Tabs value={selectedTab} onValueChange={setSelectedTab} className="space-y-4">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="overview">
                  <BarChart3 className="h-4 w-4 mr-2" />
                  Vue d'ensemble
                </TabsTrigger>
                <TabsTrigger value="users">
                  <Users className="h-4 w-4 mr-2" />
                  Utilisateurs
                </TabsTrigger>
                <TabsTrigger value="tickets">
                  <MessageSquare className="h-4 w-4 mr-2" />
                  Tickets
                </TabsTrigger>
                <TabsTrigger value="content">
                  <FileText className="h-4 w-4 mr-2" />
                  Contenu
                </TabsTrigger>
              </TabsList>

              {/* Vue d'ensemble */}
              <TabsContent value="overview" className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  {/* Activité récente */}
                  <Card className="bg-base-100/95 backdrop-blur border-orange-500/20 shadow-lg hover:shadow-xl transition-all duration-300">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2 text-base-content">
                        <Activity className="h-5 w-5 text-orange-400" />
                        Activité Récente
                      </CardTitle>
                      <CardDescription className="text-base-content/70">
                        Dernières actions administratives
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {mockRecentActions.map((action) => (
                        <div key={action.id} className="flex items-start gap-3 p-3 rounded-lg bg-base-100/50">
                          {getActionIcon(action.type)}
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium truncate text-base-content">{action.action}</p>
                            <p className="text-xs text-base-content/70">
                              Par {action.user} • {action.timestamp}
                            </p>
                          </div>
                        </div>
                      ))}
                    </CardContent>
                  </Card>

                  {/* Métriques de performance */}
                  <Card className="bg-base-100/95 backdrop-blur border-cyan-500/20 shadow-lg hover:shadow-xl transition-all duration-300">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2 text-base-content">
                        <TrendingUp className="h-5 w-5 text-cyan-400" />
                        Métriques de Performance
                      </CardTitle>
                      <CardDescription className="text-base-content/70">
                        Indicateurs clés du système
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm text-base-content">
                          <span>Uptime Serveur</span>
                          <span className="font-medium">{mockStats.serverUptime}%</span>
                        </div>
                        <Progress value={mockStats.serverUptime} className="h-2" />
                      </div>
                      
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm text-base-content">
                          <span>Utilisateurs Actifs</span>
                          <span className="font-medium">{Math.round((mockStats.activeUsers / mockStats.totalUsers) * 100)}%</span>
                        </div>
                        <Progress value={(mockStats.activeUsers / mockStats.totalUsers) * 100} className="h-2" />
                      </div>
                      
                      <div className="grid grid-cols-2 gap-4 pt-4">
                        <div className="text-center p-3 bg-base-100/50 rounded-lg">
                          <div className="text-2xl font-bold text-base-content">{mockStats.dailyVisits.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",")}</div>
                          <div className="text-xs text-base-content/70">Visites aujourd'hui</div>
                        </div>
                        <div className="text-center p-3 bg-base-100/50 rounded-lg">
                          <div className="text-2xl font-bold text-base-content">{mockStats.pendingTickets}</div>
                          <div className="text-xs text-base-content/70">Tickets en attente</div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              {/* Gestion des utilisateurs */}
              <TabsContent value="users" className="space-y-4">
                <Card className="bg-base-100/95 backdrop-blur border-blue-500/20 shadow-lg hover:shadow-xl transition-all duration-300">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle className="flex items-center gap-2 text-base-content">
                          <Users className="h-5 w-5 text-blue-400" />
                          Gestion des Utilisateurs
                        </CardTitle>
                        <CardDescription className="text-base-content/70">
                          Administrez les comptes et permissions
                        </CardDescription>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="relative">
                          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                          <Input
                            placeholder="Rechercher un utilisateur..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className="pl-8 w-64"
                          />
                        </div>
                        <Button size="sm">
                          <Plus className="h-4 w-4 mr-2" />
                          Nouvel utilisateur
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Utilisateur</TableHead>
                          <TableHead>Rôle</TableHead>
                          <TableHead>Statut</TableHead>
                          <TableHead>Dernière activité</TableHead>
                          <TableHead>Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {mockUsers.map((user) => (
                          <TableRow key={user.id}>
                            <TableCell>
                              <div className="flex items-center gap-3">
                                <Avatar className="h-8 w-8">
                                  <AvatarImage src={`/placeholder-user.jpg`} />
                                  <AvatarFallback>{user.username.slice(0, 2).toUpperCase()}</AvatarFallback>
                                </Avatar>
                                <div>
                                  <div className="font-medium">{user.username}</div>
                                  <div className="text-sm text-muted-foreground">{user.email}</div>
                                </div>
                              </div>
                            </TableCell>
                            <TableCell>
                              <Badge variant={user.role === "Modérateur" ? "default" : "secondary"}>
                                {user.role}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              <Badge variant={user.status === "En ligne" ? "default" : "secondary"}>
                                {user.status}
                              </Badge>
                            </TableCell>
                            <TableCell className="text-sm text-muted-foreground">
                              {user.lastActive}
                            </TableCell>
                            <TableCell>
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="sm">
                                    <Settings className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent>
                                  <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                  <DropdownMenuItem>
                                    <Eye className="h-4 w-4 mr-2" />
                                    Voir le profil
                                  </DropdownMenuItem>
                                  <DropdownMenuItem>
                                    <Edit3 className="h-4 w-4 mr-2" />
                                    Modifier
                                  </DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem className="text-red-600">
                                    <Shield className="h-4 w-4 mr-2" />
                                    Modérer
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Gestion des tickets */}
              <TabsContent value="tickets" className="space-y-4">
                <Card className="bg-base-100/95 backdrop-blur border-red-500/20 shadow-lg hover:shadow-xl transition-all duration-300">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle className="flex items-center gap-2 text-base-content">
                          <MessageSquare className="h-5 w-5 text-red-400" />
                          Gestion des Tickets
                        </CardTitle>
                        <CardDescription className="text-base-content/70">
                          Support et demandes des utilisateurs
                        </CardDescription>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button variant="outline" size="sm">
                          <Filter className="h-4 w-4 mr-2" />
                          Filtrer
                        </Button>
                        <Button size="sm">
                          <Plus className="h-4 w-4 mr-2" />
                          Nouveau ticket
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>ID</TableHead>
                          <TableHead>Titre</TableHead>
                          <TableHead>Utilisateur</TableHead>
                          <TableHead>Priorité</TableHead>
                          <TableHead>Statut</TableHead>
                          <TableHead>Catégorie</TableHead>
                          <TableHead>Créé le</TableHead>
                          <TableHead>Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {mockTickets.map((ticket) => (
                          <TableRow key={ticket.id}>
                            <TableCell className="font-medium">#{ticket.id}</TableCell>
                            <TableCell className="max-w-xs truncate">{ticket.title}</TableCell>
                            <TableCell>{ticket.user}</TableCell>
                            <TableCell>
                              <div className="flex items-center gap-2">
                                <div className={`w-2 h-2 rounded-full ${getPriorityColor(ticket.priority)}`} />
                                {ticket.priority}
                              </div>
                            </TableCell>
                            <TableCell>
                              <Badge className={getStatusColor(ticket.status)}>
                                {ticket.status}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              <Badge variant="outline">{ticket.category}</Badge>
                            </TableCell>
                            <TableCell className="text-sm text-muted-foreground">
                              {ticket.created}
                            </TableCell>
                            <TableCell>
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="sm">
                                    <Settings className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent>
                                  <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                  <DropdownMenuItem>
                                    <Eye className="h-4 w-4 mr-2" />
                                    Voir détails
                                  </DropdownMenuItem>
                                  <DropdownMenuItem>
                                    <Edit3 className="h-4 w-4 mr-2" />
                                    Répondre
                                  </DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem>
                                    <CheckCircle className="h-4 w-4 mr-2" />
                                    Marquer résolu
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Gestion du contenu */}
              <TabsContent value="content" className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <Card className="bg-base-100/95 backdrop-blur border-purple-500/20 shadow-lg hover:shadow-xl transition-all duration-300">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2 text-base-content">
                        <FileText className="h-5 w-5 text-purple-400" />
                        Gestion du Règlement
                      </CardTitle>
                      <CardDescription className="text-base-content/70">
                        Créer, modifier et publier les sections et règles
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid gap-2">
                        <Button variant="outline" className="justify-start" asChild>
                          <a href="/admin/reglement">
                            <Edit3 className="h-4 w-4 mr-2" />
                            Gérer les Sections
                          </a>
                        </Button>
                        <Button variant="outline" className="justify-start" asChild>
                          <a href="/admin/reglement/new">
                            <Plus className="h-4 w-4 mr-2" />
                            Nouvelle Section
                          </a>
                        </Button>
                        <Button variant="outline" className="justify-start" asChild>
                          <a href="/reglement">
                            <Eye className="h-4 w-4 mr-2" />
                            Voir le Règlement Public
                          </a>
                        </Button>
                      </div>
                      <div className="text-sm text-base-content/70 mt-4">
                        {stats ? `${stats.published_sections || 0} sections publiées` : 'Chargement...'}
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="bg-base-100/95 backdrop-blur border-blue-500/20 shadow-lg hover:shadow-xl transition-all duration-300">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2 text-base-content">
                        <BookOpen className="h-5 w-5 text-blue-400" />
                        Gestion du Lore
                      </CardTitle>
                      <CardDescription className="text-base-content/70">
                        Gérer les chapitres et l'histoire du royaume
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid gap-2">
                        <Button variant="outline" className="justify-start" asChild>
                          <a href="/admin/lore">
                            <Edit3 className="h-4 w-4 mr-2" />
                            Gérer les Chapitres
                          </a>
                        </Button>
                        <Button variant="outline" className="justify-start" asChild>
                          <a href="/admin/lore/new">
                            <Plus className="h-4 w-4 mr-2" />
                            Nouveau Chapitre
                          </a>
                        </Button>
                        <Button variant="outline" className="justify-start" asChild>
                          <a href="/lore">
                            <Eye className="h-4 w-4 mr-2" />
                            Voir le Lore Public
                          </a>
                        </Button>
                      </div>
                      <div className="text-sm text-base-content/70 mt-4">
                        {stats ? `${stats.published_chapters || 0} chapitres publiés` : 'Chargement...'}
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="bg-base-100/95 backdrop-blur border-green-500/20 shadow-lg hover:shadow-xl transition-all duration-300">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2 text-base-content">
                        <Upload className="h-5 w-5 text-green-400" />
                        Gestion des Médias
                      </CardTitle>
                      <CardDescription className="text-base-content/70">
                        Upload et organisation des images
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="border-2 border-dashed border-base-content/25 rounded-lg p-6 text-center">
                        <Upload className="h-8 w-8 mx-auto mb-2 text-base-content/70" />
                        <p className="text-sm text-base-content/70 mb-2">
                          Glissez-déposez vos images ici
                        </p>
                        <Button size="sm" variant="outline">
                          Choisir des fichiers
                        </Button>
                      </div>
                      
                      <div className="space-y-2">
                        <h4 className="text-sm font-medium text-base-content">Images récentes</h4>
                        <div className="grid grid-cols-3 gap-2">
                          {[1, 2, 3].map((i) => (
                            <div key={i} className="aspect-square bg-base-100/50 rounded-lg flex items-center justify-center">
                              <span className="text-xs text-base-content/70">Image {i}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>
            </Tabs>
          </motion.div>
        </motion.div>
      </div>
    </>
  )
}