/**
 * Database Initialization Script
 * Run this script to set up the new content management database schema
 * 
 * Usage: bun run scripts/init-database.ts
 */

import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function initializeDatabase() {
  console.log('🚀 Starting database initialization...')

  try {
    // Step 1: Drop old stock tables
    console.log('📦 Dropping old stock management tables...')
    
    const dropQueries = [
      'DROP VIEW IF EXISTS stock_summary CASCADE;',
      'DROP VIEW IF EXISTS stock_items_with_category CASCADE;',
      'DROP TABLE IF EXISTS stock_alerts CASCADE;',
      'DROP TABLE IF EXISTS stock_transactions CASCADE;',
      'DROP TABLE IF EXISTS stock_items CASCADE;',
      'DROP TABLE IF EXISTS stock_categories CASCADE;'
    ]

    for (const query of dropQueries) {
      const { error } = await supabase.rpc('exec_sql', { sql: query })
      if (error) {
        console.warn(`Warning dropping table: ${error.message}`)
      }
    }

    // Step 2: Create new content management tables
    console.log('📝 Creating content management tables...')
    
    // Read the schema file
    const fs = require('fs')
    const path = require('path')
    const schemaPath = path.join(process.cwd(), 'database', 'schema.sql')
    const schema = fs.readFileSync(schemaPath, 'utf8')
    
    // Split schema into individual statements
    const statements = schema
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'))

    for (const statement of statements) {
      if (statement.trim()) {
        const { error } = await supabase.rpc('exec_sql', { sql: statement + ';' })
        if (error) {
          console.error(`Error executing statement: ${error.message}`)
          console.error(`Statement: ${statement.substring(0, 100)}...`)
        }
      }
    }

    console.log('✅ Database initialization completed successfully!')
    console.log('🎉 Your content management system is ready to use!')
    
  } catch (error) {
    console.error('❌ Database initialization failed:', error)
    process.exit(1)
  }
}

// Run the initialization
initializeDatabase()
