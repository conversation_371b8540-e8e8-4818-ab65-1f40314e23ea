"use client"

import { useState, useEffect } from "react"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { Card, CardHeader, CardTitle, CardContent, CardDescription } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { ChevronLeft, ChevronRight, BookOpen, Clock, Users, AlertTriangle } from "lucide-react"
import { motion } from "framer-motion"
import { useContent } from "@/hooks/use-stock"
import { LoreChapter } from "@/lib/supabase"

// Transform database chapters to match the expected format
interface TransformedChapter {
  id: number
  title: string
  subtitle: string
  readTime: string
  content: {
    introduction: string
    mainText: string
    sections: Array<{
      title: string
      text: string
    }>
  }
}

export default function LorePage() {
  const [currentChapter, setCurrentChapter] = useState(1)
  const [isReading, setIsReading] = useState(false)
  const [transformedChapters, setTransformedChapters] = useState<TransformedChapter[]>([])
  const { loreChapters, loading, error, fetchLoreChapters } = useContent()

  useEffect(() => {
    fetchLoreChapters()
  }, [fetchLoreChapters])

  useEffect(() => {
    // Transform database chapters to match the expected format
    const transformed: TransformedChapter[] = loreChapters.map(chapter => ({
      id: chapter.chapter_number,
      title: chapter.title,
      subtitle: chapter.excerpt || 'Chapitre du Royaume des Tempêtes',
      readTime: `${Math.max(1, Math.ceil(chapter.content.length / 1000))} min`,
      content: {
        introduction: chapter.excerpt || '',
        mainText: chapter.content,
        sections: [
          {
            title: chapter.title,
            text: chapter.content
          }
        ]
      }
    }))
    setTransformedChapters(transformed.sort((a, b) => a.id - b.id))
  }, [loreChapters])

  // Show loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="text-base-content/70">Chargement des chroniques...</p>
        </div>
      </div>
    )
  }

  // Show error state
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center space-y-4">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto" />
          <h2 className="text-xl font-semibold text-base-content">Erreur de chargement</h2>
          <p className="text-base-content/70">{error}</p>
          <Button onClick={() => fetchLoreChapters()} variant="outline">
            Réessayer
          </Button>
        </div>
      </div>
    )
  }

  const chapter = transformedChapters.find(ch => ch.id === currentChapter) || transformedChapters[0]
  const totalChapters = transformedChapters.length

  // Show empty state if no chapters
  if (!loading && !error && transformedChapters.length === 0) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center space-y-4">
          <BookOpen className="h-12 w-12 text-base-content/50 mx-auto" />
          <h2 className="text-xl font-semibold text-base-content">Aucun chapitre disponible</h2>
          <p className="text-base-content/70">Les chroniques du royaume seront bientôt disponibles.</p>
        </div>
      </div>
    )
  }

  const nextChapter = () => {
    if (currentChapter < totalChapters) {
      setCurrentChapter(currentChapter + 1)
    }
  }

  const prevChapter = () => {
    if (currentChapter > 1) {
      setCurrentChapter(currentChapter - 1)
    }
  }

  const goToChapter = (chapterId: number) => {
    setCurrentChapter(chapterId)
    setIsReading(true)
  }

  return (
    <div>
      <header className="flex h-16 shrink-0 items-center gap-2 border-b border-base-300/50 bg-base-100/95 backdrop-blur supports-[backdrop-filter]:bg-base-100/80 px-4 shadow-sm relative z-10">
        <SidebarTrigger className="-ml-1 hover:bg-primary/10 transition-colors" />
        <Separator orientation="vertical" className="mr-2 h-4 bg-base-300/50" />
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem className="hidden md:block">
              <BreadcrumbLink href="/" className="text-base-content/60 hover:text-base-content transition-colors">
                Dashboard
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator className="hidden md:block text-base-content/40" />
            <BreadcrumbItem>
              <BreadcrumbPage className="text-base-content font-medium">Lore</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </header>
      <main className="flex-1 p-6 min-h-screen relative overflow-hidden">
        {/* Consistent Background Image */}
        <motion.div
          initial={{ opacity: 0, scale: 1.1 }}
          animate={{ opacity: 0.15, scale: 1 }}
          transition={{ duration: 2 }}
          className="absolute inset-0 z-0"
        >
          <img src="/image3.png" alt="" className="w-full h-full object-cover opacity-15 mix-blend-soft-light" />
        </motion.div>

        <div className="relative z-10 max-w-7xl mx-auto">
          {!isReading ? (
            // Chapter Selection View
            <div className="space-y-8">
              {/* Header */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                className="text-center space-y-4"
              >
                <div className="flex items-center justify-center gap-3 mb-4">
                  <BookOpen className="h-8 w-8 text-primary" />
                  <h1 className="text-4xl font-bold text-base-content">Chroniques du Royaume</h1>
                </div>
                <p className="text-lg text-base-content/70 max-w-2xl mx-auto">
                  Découvrez l'histoire fascinante du Royaume des Tempêtes à travers ses différentes époques
                </p>
              </motion.div>

              {/* Chapter Grid */}
              <motion.div
                initial={{ opacity: 0, y: 40 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                className="grid gap-6 md:grid-cols-2 lg:grid-cols-3"
              >
                {transformedChapters.map((chapterItem, index) => (
                  <motion.div
                    key={chapterItem.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    whileHover={{ scale: 1.02, y: -5 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Card
                      className="bg-base-100/95 backdrop-blur border-primary/20 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer h-full"
                      onClick={() => goToChapter(chapterItem.id)}
                    >
                      <CardHeader>
                        <div className="flex items-start justify-between">
                          <Badge className="bg-primary/20 text-primary border-primary/30">
                            Chapitre {chapterItem.id}
                          </Badge>
                          <div className="flex items-center gap-1 text-xs text-base-content/60">
                            <Clock className="h-3 w-3" />
                            {chapterItem.readTime}
                          </div>
                        </div>
                        <CardTitle className="text-base-content text-xl">
                          {chapterItem.title}
                        </CardTitle>
                        <CardDescription className="text-base-content/70">
                          {chapterItem.subtitle}
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <p className="text-sm text-base-content/80 line-clamp-3">
                          {chapterItem.content.introduction}
                        </p>
                        <div className="mt-4 flex items-center justify-between">
                          <span className="text-xs text-base-content/60">
                            {chapterItem.content.sections.length} sections
                          </span>
                          <ChevronRight className="h-4 w-4 text-primary" />
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </motion.div>
            </div>
          ) : (
            // Chapter Reading View
            <div className="space-y-8">
              {/* Chapter Header */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                className="space-y-4"
              >
                <Button
                  variant="outline"
                  onClick={() => setIsReading(false)}
                  className="mb-4 border-base-300 hover:bg-base-200"
                >
                  <ChevronLeft className="h-4 w-4 mr-2" />
                  Retour aux chapitres
                </Button>

                <div className="text-center space-y-2">
                  <Badge className="bg-primary/20 text-primary border-primary/30">
                    Chapitre {chapter.id} sur {totalChapters}
                  </Badge>
                  <h1 className="text-3xl font-bold text-base-content">{chapter.title}</h1>
                  <p className="text-lg text-base-content/70">{chapter.subtitle}</p>
                  <div className="flex items-center justify-center gap-4 text-sm text-base-content/60">
                    <div className="flex items-center gap-1">
                      <Clock className="h-4 w-4" />
                      {chapter.readTime}
                    </div>
                    <div className="flex items-center gap-1">
                      <Users className="h-4 w-4" />
                      {chapter.content.sections.length} sections
                    </div>
                  </div>
                </div>
              </motion.div>

              {/* Chapter Content */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.3 }}
              >
                <Card className="bg-base-100/95 backdrop-blur border-primary/20 shadow-lg">
                  <CardHeader>
                    <CardTitle className="text-base-content text-2xl">
                      {chapter.title}
                    </CardTitle>
                    <CardDescription className="text-base-content/70 text-lg">
                      {chapter.content.introduction}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="prose prose-lg max-w-none">
                      <p className="text-base-content leading-relaxed">
                        {chapter.content.mainText}
                      </p>
                    </div>

                    {/* Chapter Sections */}
                    <div className="space-y-6">
                      {chapter.content.sections.map((section, index) => (
                        <motion.div
                          key={index}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ duration: 0.6, delay: 0.4 + index * 0.1 }}
                          className="border-l-4 border-primary/30 pl-6 py-4 bg-primary/5 rounded-r-lg"
                        >
                          <h3 className="text-xl font-semibold text-base-content mb-3">
                            {section.title}
                          </h3>
                          <p className="text-base-content/80 leading-relaxed">
                            {section.text}
                          </p>
                        </motion.div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>

              {/* Navigation */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.5 }}
                className="flex items-center justify-between"
              >
                <Button
                  variant="outline"
                  onClick={prevChapter}
                  disabled={currentChapter === 1}
                  className="border-base-300 hover:bg-base-200 disabled:opacity-50"
                >
                  <ChevronLeft className="h-4 w-4 mr-2" />
                  Chapitre précédent
                </Button>

                {/* Page Indicators */}
                <div className="flex items-center gap-2">
                  {transformedChapters.map((_, index) => (
                    <button
                      key={index + 1}
                      onClick={() => setCurrentChapter(index + 1)}
                      className={`w-8 h-8 rounded-full text-sm font-medium transition-all duration-200 ${
                        currentChapter === index + 1
                          ? 'bg-primary text-primary-content'
                          : 'bg-base-200 text-base-content hover:bg-base-300'
                      }`}
                    >
                      {index + 1}
                    </button>
                  ))}
                </div>

                <Button
                  variant="outline"
                  onClick={nextChapter}
                  disabled={currentChapter === totalChapters}
                  className="border-base-300 hover:bg-base-200 disabled:opacity-50"
                >
                  Chapitre suivant
                  <ChevronRight className="h-4 w-4 ml-2" />
                </Button>
              </motion.div>
            </div>
          )}
        </div>
      </main>
    </div>
  )
}