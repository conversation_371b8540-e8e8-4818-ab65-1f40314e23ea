import { NextRequest, NextResponse } from 'next/server'
import { adminService } from '@/lib/supabase'

export async function GET() {
  try {
    const tickets = await adminService.getAllTickets()
    return NextResponse.json({ success: true, data: tickets })
  } catch (error) {
    console.error('Error fetching tickets:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch tickets' },
      { status: 500 }
    )
  }
}
