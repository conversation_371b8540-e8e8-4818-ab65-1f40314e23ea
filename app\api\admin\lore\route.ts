import { NextRequest, NextResponse } from 'next/server'
import { adminService } from '@/lib/supabase'

export async function GET() {
  try {
    const chapters = await adminService.getAllLoreChapters()
    return NextResponse.json({ success: true, data: chapters })
  } catch (error) {
    console.error('Error fetching lore chapters:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch lore chapters' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const chapter = await adminService.createLoreChapter(body)
    return NextResponse.json({ success: true, data: chapter })
  } catch (error) {
    console.error('Error creating lore chapter:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to create lore chapter' },
      { status: 500 }
    )
  }
}
