import { NextRequest, NextResponse } from 'next/server'
import { contentService } from '@/lib/supabase'

export async function GET() {
  try {
    const chapters = await contentService.getLoreChapters()
    return NextResponse.json({ success: true, data: chapters })
  } catch (error) {
    console.error('Error fetching lore chapters:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch lore chapters' },
      { status: 500 }
    )
  }
}
