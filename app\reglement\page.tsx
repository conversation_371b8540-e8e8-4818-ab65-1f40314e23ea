"use client"

import { useState, useEffect } from "react"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { Card, CardHeader, CardTitle, CardContent, CardDescription } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"
import {
  ChevronDown,
  ChevronRight,
  Search,
  Shield,
  Users,
  Sword,
  Home,
  AlertTriangle,
  Scale,
  BookOpen,
  Link
} from "lucide-react"
import { motion } from "framer-motion"
import { useContent } from "@/hooks/use-stock"
import { ReglementSection, ReglementRule } from "@/lib/supabase"

// Icon mapping for sections
const iconMap: Record<string, any> = {
  'regles-generales': BookOpen,
  'regles-chat': Users,
  'regles-jeu': Sword,
  'sanctions': Scale,
  'default': BookOpen
}

// Color mapping for sections
const colorMap: Record<string, string> = {
  'regles-generales': 'blue',
  'regles-chat': 'green',
  'regles-jeu': 'purple',
  'sanctions': 'red',
  'default': 'blue'
}

// Transform database sections to match the expected format
interface TransformedSection {
  id: string
  title: string
  icon: any
  color: string
  description: string
  rules: Array<{
    title: string
    content: string[]
  }>
}

export default function ReglementPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [openSections, setOpenSections] = useState<string[]>([])
  const [transformedSections, setTransformedSections] = useState<TransformedSection[]>([])
  const { reglementSections, loading, error, fetchReglementSections } = useContent()

  useEffect(() => {
    fetchReglementSections()
  }, [fetchReglementSections])

  useEffect(() => {
    // Transform database sections to match the expected format
    const transformed: TransformedSection[] = reglementSections.map(section => ({
      id: section.slug,
      title: section.title,
      icon: iconMap[section.slug] || iconMap.default,
      color: colorMap[section.slug] || colorMap.default,
      description: section.description || '',
      rules: [
        {
          title: section.title,
          content: [section.description || 'Aucune description disponible']
        }
      ]
    }))
    setTransformedSections(transformed)
  }, [reglementSections])

  // Show loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="text-base-content/70">Chargement du règlement...</p>
        </div>
      </div>
    )
  }

  // Show error state
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center space-y-4">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto" />
          <h2 className="text-xl font-semibold text-base-content">Erreur de chargement</h2>
          <p className="text-base-content/70">{error}</p>
          <Button onClick={() => fetchReglementSections()} variant="outline">
            Réessayer
          </Button>
        </div>
      </div>
    )
  }

  const toggleSection = (sectionId: string) => {
    setOpenSections(prev =>
      prev.includes(sectionId)
        ? prev.filter(id => id !== sectionId)
        : [...prev, sectionId]
    )
  }

  const filteredRules = transformedSections.map(category => ({
    ...category,
    rules: category.rules.filter(rule =>
      rule.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      rule.content.some(content => content.toLowerCase().includes(searchTerm.toLowerCase()))
    )
  })).filter(category => category.rules.length > 0)

  const getColorClasses = (color: string) => {
    const colorMap = {
      blue: "border-blue-500/20 bg-blue-500/5",
      purple: "border-purple-500/20 bg-purple-500/5",
      red: "border-red-500/20 bg-red-500/5",
      green: "border-green-500/20 bg-green-500/5"
    }
    return colorMap[color as keyof typeof colorMap] || colorMap.blue
  }

  const getIconColor = (color: string) => {
    const colorMap = {
      blue: "text-blue-400",
      purple: "text-purple-400",
      red: "text-red-400",
      green: "text-green-400"
    }
    return colorMap[color as keyof typeof colorMap] || colorMap.blue
  }

  return (
    <div>
      <header className="flex h-16 shrink-0 items-center gap-2 border-b border-base-300/50 bg-base-100/95 backdrop-blur supports-[backdrop-filter]:bg-base-100/80 px-4 shadow-sm relative z-10">
        <SidebarTrigger className="-ml-1 hover:bg-primary/10 transition-colors" />
        <Separator orientation="vertical" className="mr-2 h-4 bg-base-300/50" />
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem className="hidden md:block">
              <BreadcrumbLink href="/" className="text-base-content/60 hover:text-base-content transition-colors">
                Dashboard
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator className="hidden md:block text-base-content/40" />
            <BreadcrumbItem>
              <BreadcrumbPage className="text-base-content font-medium">Règlement</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </header>
      <main className="flex-1 p-6 min-h-screen relative overflow-hidden">
        {/* Consistent Background Image */}
        <motion.div
          initial={{ opacity: 0, scale: 1.1 }}
          animate={{ opacity: 0.15, scale: 1 }}
          transition={{ duration: 2 }}
          className="absolute inset-0 z-0"
        >
          <img src="/image2.png" alt="" className="w-full h-full object-cover opacity-15 mix-blend-soft-light" />
        </motion.div>

        <div className="relative z-10 max-w-6xl mx-auto space-y-8">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center space-y-4"
          >
            <div className="flex items-center justify-center gap-3 mb-4">
              <Scale className="h-8 w-8 text-primary" />
              <h1 className="text-4xl font-bold text-base-content">Règlement du Royaume</h1>
            </div>
            <p className="text-lg text-base-content/70 max-w-3xl mx-auto">
              Consultez toutes les règles organisées par catégories. Cliquez sur une section pour voir les détails.
            </p>
          </motion.div>

          {/* Search Bar */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="max-w-md mx-auto"
          >
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-base-content/50" />
              <Input
                placeholder="Rechercher dans les règles..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 bg-base-100/80 border-base-300 focus:border-primary"
              />
            </div>
          </motion.div>

          {/* Rules Categories */}
          <motion.div
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.3 }}
            className="space-y-4"
          >
            {filteredRules.map((category, categoryIndex) => {
              const IconComponent = category.icon
              const isOpen = openSections.includes(category.id)

              return (
                <motion.div
                  key={category.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: categoryIndex * 0.1 }}
                >
                  <Collapsible open={isOpen} onOpenChange={() => toggleSection(category.id)}>
                    <Card className="bg-base-100/95 backdrop-blur border-primary/20 shadow-lg hover:shadow-xl transition-all duration-300">
                      <CollapsibleTrigger asChild>
                        <CardHeader className="cursor-pointer hover:bg-base-200/50 transition-colors">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <div className={`p-2 rounded-lg ${getColorClasses(category.color)}`}>
                                <IconComponent className={`h-5 w-5 ${getIconColor(category.color)}`} />
                              </div>
                              <div className="text-left">
                                <CardTitle className="text-base-content text-xl">
                                  {category.title}
                                </CardTitle>
                                <CardDescription className="text-base-content/70">
                                  {category.description}
                                </CardDescription>
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              <Badge className="bg-primary/20 text-primary border-primary/30">
                                {category.rules.length} règle{category.rules.length > 1 ? 's' : ''}
                              </Badge>
                              {isOpen ? (
                                <ChevronDown className="h-5 w-5 text-base-content/60" />
                              ) : (
                                <ChevronRight className="h-5 w-5 text-base-content/60" />
                              )}
                            </div>
                          </div>
                        </CardHeader>
                      </CollapsibleTrigger>

                      <CollapsibleContent>
                        <CardContent className="pt-0">
                          <div className="space-y-6">
                            {category.rules.map((rule, ruleIndex) => (
                              <motion.div
                                key={ruleIndex}
                                initial={{ opacity: 0, y: 10 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.4, delay: ruleIndex * 0.1 }}
                                className="border-l-4 border-primary/30 pl-6 py-4 bg-primary/5 rounded-r-lg"
                              >
                                <h3 className="text-lg font-semibold text-base-content mb-3">
                                  {rule.title}
                                </h3>
                                <ul className="space-y-2">
                                  {rule.content.map((item, itemIndex) => (
                                    <li key={itemIndex} className="flex items-start gap-2 text-base-content/80">
                                      <div className="w-1.5 h-1.5 rounded-full bg-primary/60 mt-2 flex-shrink-0" />
                                      <span className="leading-relaxed">{item}</span>
                                    </li>
                                  ))}
                                </ul>
                              </motion.div>
                            ))}
                          </div>
                        </CardContent>
                      </CollapsibleContent>
                    </Card>
                  </Collapsible>
                </motion.div>
              )
            })}
          </motion.div>

          {/* Quick Links */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
            className="mt-12"
          >
            <Card className="bg-base-100/95 backdrop-blur border-primary/20 shadow-lg">
              <CardHeader>
                <CardTitle className="text-base-content flex items-center gap-2">
                  <Link className="h-5 w-5" />
                  Liens Rapides
                </CardTitle>
                <CardDescription className="text-base-content/70">
                  Accès direct aux sections importantes
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-5 gap-3">
                  {transformedSections.map((category) => {
                    const IconComponent = category.icon
                    return (
                      <Button
                        key={category.id}
                        variant="outline"
                        onClick={() => toggleSection(category.id)}
                        className="h-auto p-3 flex flex-col items-center gap-2 border-base-300 hover:bg-base-200"
                      >
                        <IconComponent className={`h-4 w-4 ${getIconColor(category.color)}`} />
                        <span className="text-xs text-center">{category.title}</span>
                      </Button>
                    )
                  })}
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* No Results Message */}
          {searchTerm && filteredRules.length === 0 && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="text-center py-12"
            >
              <AlertTriangle className="h-12 w-12 text-base-content/40 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-base-content mb-2">Aucun résultat trouvé</h3>
              <p className="text-base-content/60">
                Essayez avec d'autres mots-clés ou parcourez les catégories ci-dessus.
              </p>
            </motion.div>
          )}
        </div>
      </main>
    </div>
  )
}