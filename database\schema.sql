-- Content Management Database Schema for Le Royaume Des Tempêtes
-- This file contains the complete database schema for content management
-- Handles règlement (rules), lore pages, and ticket management for admin panel
-- User management is handled by Discord OAuth, so we store Discord user IDs as strings

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create règlement (rules) sections table
CREATE TABLE IF NOT EXISTS reglement_sections (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL UNIQUE, -- URL-friendly identifier
    description TEXT,
    order_index INTEGER NOT NULL DEFAULT 0,
    is_published BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by <PERSON><PERSON><PERSON><PERSON>(255), -- Discord user ID as string
    updated_by VARC<PERSON>R(255)  -- Discord user ID as string
);

-- Create règlement rules table
CREATE TABLE IF NOT EXISTS reglement_rules (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    section_id UUID NOT NULL REFERENCES reglement_sections(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    order_index INTEGER NOT NULL DEFAULT 0,
    is_published BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by VARCHAR(255), -- Discord user ID as string
    updated_by VARCHAR(255)  -- Discord user ID as string
);

-- Create lore chapters table
CREATE TABLE IF NOT EXISTS lore_chapters (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL UNIQUE, -- URL-friendly identifier
    content TEXT NOT NULL,
    excerpt TEXT, -- Short description for chapter list
    chapter_number INTEGER NOT NULL,
    is_published BOOLEAN NOT NULL DEFAULT false,
    featured_image VARCHAR(500), -- URL to chapter image
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by VARCHAR(255), -- Discord user ID as string
    updated_by VARCHAR(255)  -- Discord user ID as string
);

-- Create ticket categories table
CREATE TABLE IF NOT EXISTS ticket_categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    color VARCHAR(7), -- Hex color code
    icon VARCHAR(50), -- Icon name for UI
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create tickets table
CREATE TABLE IF NOT EXISTS tickets (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    category_id UUID NOT NULL REFERENCES ticket_categories(id) ON DELETE RESTRICT,
    status VARCHAR(20) NOT NULL DEFAULT 'open' CHECK (status IN ('open', 'in_progress', 'resolved', 'closed')),
    priority VARCHAR(10) NOT NULL DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    created_by VARCHAR(255) NOT NULL, -- Discord user ID as string
    assigned_to VARCHAR(255), -- Discord user ID as string
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    resolved_at TIMESTAMP WITH TIME ZONE,
    resolved_by VARCHAR(255) -- Discord user ID as string
);

-- Create ticket comments table
CREATE TABLE IF NOT EXISTS ticket_comments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    ticket_id UUID NOT NULL REFERENCES tickets(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    is_internal BOOLEAN NOT NULL DEFAULT false, -- Internal admin notes vs public comments
    created_by VARCHAR(255) NOT NULL, -- Discord user ID as string
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create admin users table (for role management)
CREATE TABLE IF NOT EXISTS admin_users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    discord_id VARCHAR(255) NOT NULL UNIQUE,
    username VARCHAR(255) NOT NULL,
    avatar_url VARCHAR(500),
    role VARCHAR(20) NOT NULL DEFAULT 'moderator' CHECK (role IN ('admin', 'moderator', 'editor')),
    permissions JSONB, -- Flexible permissions system
    is_active BOOLEAN NOT NULL DEFAULT true,
    last_login TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_reglement_sections_published ON reglement_sections(is_published);
CREATE INDEX IF NOT EXISTS idx_reglement_sections_order ON reglement_sections(order_index);
CREATE INDEX IF NOT EXISTS idx_reglement_rules_section ON reglement_rules(section_id);
CREATE INDEX IF NOT EXISTS idx_reglement_rules_published ON reglement_rules(is_published);
CREATE INDEX IF NOT EXISTS idx_reglement_rules_order ON reglement_rules(order_index);
CREATE INDEX IF NOT EXISTS idx_lore_chapters_published ON lore_chapters(is_published);
CREATE INDEX IF NOT EXISTS idx_lore_chapters_number ON lore_chapters(chapter_number);
CREATE INDEX IF NOT EXISTS idx_tickets_status ON tickets(status);
CREATE INDEX IF NOT EXISTS idx_tickets_priority ON tickets(priority);
CREATE INDEX IF NOT EXISTS idx_tickets_category ON tickets(category_id);
CREATE INDEX IF NOT EXISTS idx_tickets_created_by ON tickets(created_by);
CREATE INDEX IF NOT EXISTS idx_tickets_assigned_to ON tickets(assigned_to);
CREATE INDEX IF NOT EXISTS idx_ticket_comments_ticket ON ticket_comments(ticket_id);
CREATE INDEX IF NOT EXISTS idx_admin_users_discord_id ON admin_users(discord_id);
CREATE INDEX IF NOT EXISTS idx_admin_users_role ON admin_users(role);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_reglement_sections_updated_at
    BEFORE UPDATE ON reglement_sections
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_reglement_rules_updated_at
    BEFORE UPDATE ON reglement_rules
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_lore_chapters_updated_at
    BEFORE UPDATE ON lore_chapters
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_ticket_categories_updated_at
    BEFORE UPDATE ON ticket_categories
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tickets_updated_at
    BEFORE UPDATE ON tickets
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_ticket_comments_updated_at
    BEFORE UPDATE ON ticket_comments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_admin_users_updated_at
    BEFORE UPDATE ON admin_users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default ticket categories
INSERT INTO ticket_categories (name, description, color, icon) VALUES
('Bug Report', 'Signalement de bugs et problèmes techniques', '#FF6B6B', 'Bug'),
('Feature Request', 'Demandes de nouvelles fonctionnalités', '#4ECDC4', 'Lightbulb'),
('Support', 'Demandes d''aide et support général', '#45B7D1', 'HelpCircle'),
('Modération', 'Questions liées à la modération', '#96CEB4', 'Shield'),
('Événement', 'Organisation et gestion d''événements', '#FFEAA7', 'Calendar'),
('Autre', 'Autres demandes non catégorisées', '#A0A0A0', 'MessageSquare')
ON CONFLICT (name) DO NOTHING;

-- Insert sample règlement sections
INSERT INTO reglement_sections (title, slug, description, order_index, is_published) VALUES
('Règles Générales', 'regles-generales', 'Règles de base du serveur Discord', 1, true),
('Règles de Chat', 'regles-chat', 'Règles spécifiques aux canaux de discussion', 2, true),
('Règles de Jeu', 'regles-jeu', 'Règles pour les activités de jeu', 3, true),
('Sanctions', 'sanctions', 'Système de sanctions et procédures', 4, true)
ON CONFLICT (slug) DO NOTHING;

-- Insert sample règlement rules
INSERT INTO reglement_rules (section_id, title, content, order_index, is_published) VALUES
((SELECT id FROM reglement_sections WHERE slug = 'regles-generales'),
 'Respect mutuel',
 'Tous les membres doivent faire preuve de respect envers les autres. Les insultes, le harcèlement et les comportements toxiques ne sont pas tolérés.',
 1, true),
((SELECT id FROM reglement_sections WHERE slug = 'regles-generales'),
 'Pas de spam',
 'Le spam sous toutes ses formes est interdit. Cela inclut les messages répétitifs, les mentions abusives et le flood.',
 2, true),
((SELECT id FROM reglement_sections WHERE slug = 'regles-chat'),
 'Langage approprié',
 'Utilisez un langage approprié dans tous les canaux. Les propos discriminatoires, racistes ou offensants sont strictement interdits.',
 1, true),
((SELECT id FROM reglement_sections WHERE slug = 'regles-jeu'),
 'Fair-play',
 'Le fair-play est de mise dans toutes les activités de jeu. La triche et l''exploitation de bugs sont interdites.',
 1, true);

-- Insert sample lore chapters
INSERT INTO lore_chapters (title, slug, content, excerpt, chapter_number, is_published, featured_image) VALUES
('La Fondation du Royaume', 'fondation-royaume',
 'Il y a des siècles, dans les terres mystérieuses où les tempêtes magiques dansent éternellement...',
 'L''histoire de la création du Royaume des Tempêtes et de ses premiers dirigeants.',
 1, true, '/images/chapter1.jpg'),
('Les Grandes Tempêtes', 'grandes-tempetes',
 'Les tempêtes qui donnent son nom au royaume ne sont pas ordinaires...',
 'Découvrez les mystères des tempêtes magiques qui façonnent le royaume.',
 2, true, '/images/chapter2.jpg'),
('Les Guildes Légendaires', 'guildes-legendaires',
 'Avant les guildes actuelles, de grandes organisations dominaient le royaume...',
 'L''histoire des anciennes guildes et leur influence sur le royaume moderne.',
 3, false, '/images/chapter3.jpg');

-- Row Level Security (RLS) Policies
-- Enable RLS on all tables
ALTER TABLE reglement_sections ENABLE ROW LEVEL SECURITY;
ALTER TABLE reglement_rules ENABLE ROW LEVEL SECURITY;
ALTER TABLE lore_chapters ENABLE ROW LEVEL SECURITY;
ALTER TABLE ticket_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE tickets ENABLE ROW LEVEL SECURITY;
ALTER TABLE ticket_comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_users ENABLE ROW LEVEL SECURITY;

-- Create policies for public read access and admin write access
-- Règlement sections - public read, admin write
CREATE POLICY "Public read access for published reglement sections" ON reglement_sections
    FOR SELECT USING (is_published = true);
CREATE POLICY "Admin full access to reglement sections" ON reglement_sections
    FOR ALL USING (EXISTS (SELECT 1 FROM admin_users WHERE discord_id = current_setting('app.current_user_id', true) AND is_active = true));

-- Règlement rules - public read, admin write
CREATE POLICY "Public read access for published reglement rules" ON reglement_rules
    FOR SELECT USING (is_published = true);
CREATE POLICY "Admin full access to reglement rules" ON reglement_rules
    FOR ALL USING (EXISTS (SELECT 1 FROM admin_users WHERE discord_id = current_setting('app.current_user_id', true) AND is_active = true));

-- Lore chapters - public read, admin write
CREATE POLICY "Public read access for published lore chapters" ON lore_chapters
    FOR SELECT USING (is_published = true);
CREATE POLICY "Admin full access to lore chapters" ON lore_chapters
    FOR ALL USING (EXISTS (SELECT 1 FROM admin_users WHERE discord_id = current_setting('app.current_user_id', true) AND is_active = true));

-- Ticket categories - public read, admin write
CREATE POLICY "Public read access to ticket categories" ON ticket_categories
    FOR SELECT USING (is_active = true);
CREATE POLICY "Admin full access to ticket categories" ON ticket_categories
    FOR ALL USING (EXISTS (SELECT 1 FROM admin_users WHERE discord_id = current_setting('app.current_user_id', true) AND is_active = true));

-- Tickets - users can create and view their own, admins can view all
CREATE POLICY "Users can create tickets" ON tickets
    FOR INSERT WITH CHECK (created_by = current_setting('app.current_user_id', true));
CREATE POLICY "Users can view their own tickets" ON tickets
    FOR SELECT USING (created_by = current_setting('app.current_user_id', true));
CREATE POLICY "Admin full access to tickets" ON tickets
    FOR ALL USING (EXISTS (SELECT 1 FROM admin_users WHERE discord_id = current_setting('app.current_user_id', true) AND is_active = true));

-- Ticket comments - users can comment on their tickets, admins can comment on all
CREATE POLICY "Users can comment on their tickets" ON ticket_comments
    FOR INSERT WITH CHECK (EXISTS (SELECT 1 FROM tickets WHERE id = ticket_id AND created_by = current_setting('app.current_user_id', true)));
CREATE POLICY "Users can view comments on their tickets" ON ticket_comments
    FOR SELECT USING (EXISTS (SELECT 1 FROM tickets WHERE id = ticket_id AND created_by = current_setting('app.current_user_id', true)) AND is_internal = false);
CREATE POLICY "Admin full access to ticket comments" ON ticket_comments
    FOR ALL USING (EXISTS (SELECT 1 FROM admin_users WHERE discord_id = current_setting('app.current_user_id', true) AND is_active = true));

-- Admin users - only admins can manage
CREATE POLICY "Admin access to admin users" ON admin_users
    FOR ALL USING (EXISTS (SELECT 1 FROM admin_users WHERE discord_id = current_setting('app.current_user_id', true) AND role = 'admin' AND is_active = true));

-- Create views for common queries
CREATE OR REPLACE VIEW reglement_complete AS
SELECT
    rs.id as section_id,
    rs.title as section_title,
    rs.slug as section_slug,
    rs.description as section_description,
    rs.order_index as section_order,
    rr.id as rule_id,
    rr.title as rule_title,
    rr.content as rule_content,
    rr.order_index as rule_order,
    rr.is_published as rule_published
FROM reglement_sections rs
LEFT JOIN reglement_rules rr ON rs.id = rr.section_id
WHERE rs.is_published = true
ORDER BY rs.order_index, rr.order_index;

CREATE OR REPLACE VIEW lore_published AS
SELECT
    id,
    title,
    slug,
    content,
    excerpt,
    chapter_number,
    featured_image,
    created_at,
    updated_at
FROM lore_chapters
WHERE is_published = true
ORDER BY chapter_number;

CREATE OR REPLACE VIEW tickets_with_details AS
SELECT
    t.id,
    t.title,
    t.description,
    t.status,
    t.priority,
    t.created_by,
    t.assigned_to,
    t.created_at,
    t.updated_at,
    t.resolved_at,
    t.resolved_by,
    tc.name as category_name,
    tc.color as category_color,
    tc.icon as category_icon,
    COUNT(tcm.id) as comment_count
FROM tickets t
JOIN ticket_categories tc ON t.category_id = tc.id
LEFT JOIN ticket_comments tcm ON t.id = tcm.ticket_id
GROUP BY t.id, tc.name, tc.color, tc.icon
ORDER BY t.created_at DESC;

CREATE OR REPLACE VIEW ticket_statistics AS
SELECT
    tc.name as category_name,
    COUNT(t.id) as total_tickets,
    COUNT(CASE WHEN t.status = 'open' THEN 1 END) as open_tickets,
    COUNT(CASE WHEN t.status = 'in_progress' THEN 1 END) as in_progress_tickets,
    COUNT(CASE WHEN t.status = 'resolved' THEN 1 END) as resolved_tickets,
    COUNT(CASE WHEN t.status = 'closed' THEN 1 END) as closed_tickets,
    COUNT(CASE WHEN t.priority = 'urgent' THEN 1 END) as urgent_tickets,
    COUNT(CASE WHEN t.priority = 'high' THEN 1 END) as high_priority_tickets,
    AVG(EXTRACT(EPOCH FROM (COALESCE(t.resolved_at, NOW()) - t.created_at))/3600) as avg_resolution_hours
FROM ticket_categories tc
LEFT JOIN tickets t ON tc.id = t.category_id
WHERE tc.is_active = true
GROUP BY tc.id, tc.name
ORDER BY tc.name;

CREATE OR REPLACE VIEW admin_dashboard_stats AS
SELECT
    (SELECT COUNT(*) FROM reglement_sections WHERE is_published = true) as published_sections,
    (SELECT COUNT(*) FROM reglement_rules WHERE is_published = true) as published_rules,
    (SELECT COUNT(*) FROM lore_chapters WHERE is_published = true) as published_chapters,
    (SELECT COUNT(*) FROM tickets WHERE status IN ('open', 'in_progress')) as active_tickets,
    (SELECT COUNT(*) FROM tickets WHERE created_at >= NOW() - INTERVAL '7 days') as tickets_this_week,
    (SELECT COUNT(*) FROM admin_users WHERE is_active = true) as active_admins;
