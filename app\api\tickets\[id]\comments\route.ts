import { NextRequest, NextResponse } from 'next/server'
import { contentService } from '@/lib/supabase'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const comments = await contentService.getTicketComments(params.id)
    return NextResponse.json({ success: true, data: comments })
  } catch (error) {
    console.error('Error fetching ticket comments:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch ticket comments' },
      { status: 500 }
    )
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json()
    const comment = await contentService.addTicketComment({
      ...body,
      ticket_id: params.id
    })
    return NextResponse.json({ success: true, data: comment })
  } catch (error) {
    console.error('Error adding ticket comment:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to add ticket comment' },
      { status: 500 }
    )
  }
}
