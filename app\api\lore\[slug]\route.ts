import { NextRequest, NextResponse } from 'next/server'
import { contentService } from '@/lib/supabase'

export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const chapter = await contentService.getLoreChapter(params.slug)
    return NextResponse.json({ success: true, data: chapter })
  } catch (error) {
    console.error('Error fetching lore chapter:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch lore chapter' },
      { status: 500 }
    )
  }
}
