import { NextRequest, NextResponse } from 'next/server'
import { contentService } from '@/lib/supabase'

export async function GET() {
  try {
    const categories = await contentService.getTicketCategories()
    return NextResponse.json({ success: true, data: categories })
  } catch (error) {
    console.error('Error fetching ticket categories:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch ticket categories' },
      { status: 500 }
    )
  }
}
