"use client"

import * as React from "react"
import { motion } from "framer-motion"
import { Crown, CheckCircle } from "lucide-react"

export function AdminSidebarHeader() {
  return (
      <div className="p-4 bg-base-200/50 dark:bg-base-300/20 rounded-lg">
        <div className="flex items-center gap-3 mb-2">
          <div className="relative">
            <Crown className="h-8 w-8 text-primary" />
            <div className="absolute -top-1 -right-1 w-3 h-3 bg-success rounded-full border-2 border-base-100" />
          </div>
          <div>
            <h2 className="text-lg font-bold text-base-content">
              Administration
            </h2>
            <p className="text-xs text-base-content/60">
              Panneau de contrôle
            </p>
          </div>
        </div>
        
        <motion.div
          animate={{ 
            backgroundPosition: ['0% 50%', '100% 50%', '0% 50%'],
          }}
          transition={{ 
            duration: 3, 
            repeat: Infinity, 
            ease: "linear" 
          }}
          className="text-sm font-medium text-primary"
        >
          Le Royaume Des Tempêtes
        </motion.div>
      </div>
  )
}