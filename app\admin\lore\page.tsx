"use client"

import { useEffect, useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from "@/components/ui/breadcrumb"
import { 
  BookOpen, 
  Plus, 
  Edit, 
  Trash2, 
  Eye, 
  EyeOff, 
  Save,
  ArrowLeft,
  AlertTriangle,
  CheckCircle,
  Loader2,
  Image
} from "lucide-react"
import { motion } from "framer-motion"
import { useAdminContent } from "@/hooks/use-stock"
import { LoreChapter } from "@/lib/supabase"

export default function AdminLorePage() {
  const [chapters, setChapters] = useState<LoreChapter[]>([])
  const [editingChapter, setEditingChapter] = useState<LoreChapter | null>(null)
  const [isCreating, setIsCreating] = useState(false)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)

  const { 
    getAllLoreChapters, 
    createLoreChapter, 
    updateLoreChapter,
    loading: adminLoading 
  } = useAdminContent()

  useEffect(() => {
    loadChapters()
  }, [])

  const loadChapters = async () => {
    try {
      setLoading(true)
      const data = await getAllLoreChapters()
      setChapters(data.sort((a, b) => a.chapter_number - b.chapter_number))
    } catch (err) {
      setError('Erreur lors du chargement des chapitres')
    } finally {
      setLoading(false)
    }
  }

  const handleSave = async (chapterData: Partial<LoreChapter>) => {
    try {
      setSaving(true)
      setError(null)
      
      if (editingChapter) {
        // Update existing chapter
        await updateLoreChapter(editingChapter.id, chapterData)
        setSuccess('Chapitre mis à jour avec succès')
      } else {
        // Create new chapter
        await createLoreChapter({
          title: chapterData.title || '',
          slug: chapterData.slug || '',
          content: chapterData.content || '',
          excerpt: chapterData.excerpt || '',
          chapter_number: chapterData.chapter_number || 1,
          is_published: chapterData.is_published || false,
          featured_image: chapterData.featured_image || ''
        })
        setSuccess('Chapitre créé avec succès')
      }
      
      await loadChapters()
      setEditingChapter(null)
      setIsCreating(false)
    } catch (err) {
      setError('Erreur lors de la sauvegarde')
    } finally {
      setSaving(false)
    }
  }

  const togglePublished = async (chapter: LoreChapter) => {
    try {
      await updateLoreChapter(chapter.id, {
        is_published: !chapter.is_published
      })
      await loadChapters()
      setSuccess(`Chapitre ${chapter.is_published ? 'dépublié' : 'publié'} avec succès`)
    } catch (err) {
      setError('Erreur lors de la modification du statut')
    }
  }

  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '')
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim()
  }

  const getNextChapterNumber = () => {
    if (chapters.length === 0) return 1
    return Math.max(...chapters.map(c => c.chapter_number)) + 1
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin mx-auto" />
          <p>Chargement des chapitres...</p>
        </div>
      </div>
    )
  }

  return (
    <div>
      <header className="flex h-16 shrink-0 items-center gap-2 border-b border-base-300/50 bg-base-100/95 backdrop-blur px-4 shadow-sm">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/admin" className="text-base-content/60 hover:text-base-content">
                Administration
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage className="text-base-content font-medium">Gestion du Lore</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
        
        <div className="ml-auto flex items-center gap-2">
          <Button variant="outline" size="sm" asChild>
            <a href="/lore">
              <Eye className="h-4 w-4 mr-2" />
              Voir Public
            </a>
          </Button>
          <Button 
            size="sm" 
            onClick={() => setIsCreating(true)}
            disabled={isCreating || editingChapter !== null}
          >
            <Plus className="h-4 w-4 mr-2" />
            Nouveau Chapitre
          </Button>
        </div>
      </header>

      <main className="p-6 max-w-7xl mx-auto">
        {/* Messages */}
        {error && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center gap-2"
          >
            <AlertTriangle className="h-5 w-5 text-red-500" />
            <span className="text-red-700">{error}</span>
            <Button variant="ghost" size="sm" onClick={() => setError(null)} className="ml-auto">
              ×
            </Button>
          </motion.div>
        )}

        {success && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg flex items-center gap-2"
          >
            <CheckCircle className="h-5 w-5 text-green-500" />
            <span className="text-green-700">{success}</span>
            <Button variant="ghost" size="sm" onClick={() => setSuccess(null)} className="ml-auto">
              ×
            </Button>
          </motion.div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Liste des chapitres */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold">Chapitres du Lore</h2>
              <Badge variant="outline">{chapters.length} chapitres</Badge>
            </div>

            <div className="space-y-3">
              {chapters.map((chapter) => (
                <motion.div
                  key={chapter.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  whileHover={{ scale: 1.02 }}
                >
                  <Card className="bg-base-100/95 backdrop-blur border-primary/20">
                    <CardHeader className="pb-3">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <Badge variant="outline">Chapitre {chapter.chapter_number}</Badge>
                            <Badge variant={chapter.is_published ? "default" : "secondary"}>
                              {chapter.is_published ? 'Publié' : 'Brouillon'}
                            </Badge>
                          </div>
                          <CardTitle className="text-lg">{chapter.title}</CardTitle>
                          <CardDescription className="text-sm">
                            {chapter.excerpt || 'Aucun extrait'}
                          </CardDescription>
                          {chapter.featured_image && (
                            <div className="flex items-center gap-1 mt-2 text-xs text-base-content/60">
                              <Image className="h-3 w-3" />
                              Image de couverture
                            </div>
                          )}
                        </div>
                        <div className="flex items-center gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => togglePublished(chapter)}
                          >
                            {chapter.is_published ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setEditingChapter(chapter)}
                            disabled={isCreating}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardHeader>
                  </Card>
                </motion.div>
              ))}

              {chapters.length === 0 && (
                <Card className="bg-base-100/95 backdrop-blur border-dashed border-primary/30">
                  <CardContent className="p-8 text-center">
                    <BookOpen className="h-12 w-12 text-base-content/50 mx-auto mb-4" />
                    <h3 className="text-lg font-medium mb-2">Aucun chapitre</h3>
                    <p className="text-base-content/70 mb-4">
                      Commencez par créer votre premier chapitre de lore.
                    </p>
                    <Button onClick={() => setIsCreating(true)}>
                      <Plus className="h-4 w-4 mr-2" />
                      Créer un chapitre
                    </Button>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>

          {/* Formulaire d'édition */}
          {(editingChapter || isCreating) && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h2 className="text-2xl font-bold">
                  {editingChapter ? 'Modifier le Chapitre' : 'Nouveau Chapitre'}
                </h2>
                <Button
                  variant="outline"
                  onClick={() => {
                    setEditingChapter(null)
                    setIsCreating(false)
                  }}
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Retour
                </Button>
              </div>

              <ChapterForm
                chapter={editingChapter}
                onSave={handleSave}
                saving={saving}
                generateSlug={generateSlug}
                nextChapterNumber={getNextChapterNumber()}
              />
            </div>
          )}
        </div>
      </main>
    </div>
  )
}

// Composant formulaire séparé
function ChapterForm({ 
  chapter, 
  onSave, 
  saving, 
  generateSlug,
  nextChapterNumber
}: { 
  chapter: LoreChapter | null
  onSave: (data: Partial<LoreChapter>) => void
  saving: boolean
  generateSlug: (title: string) => string
  nextChapterNumber: number
}) {
  const [formData, setFormData] = useState({
    title: chapter?.title || '',
    slug: chapter?.slug || '',
    content: chapter?.content || '',
    excerpt: chapter?.excerpt || '',
    chapter_number: chapter?.chapter_number || nextChapterNumber,
    featured_image: chapter?.featured_image || '',
    is_published: chapter?.is_published || false
  })

  const handleTitleChange = (title: string) => {
    setFormData(prev => ({
      ...prev,
      title,
      slug: prev.slug || generateSlug(title)
    }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSave(formData)
  }

  return (
    <Card className="bg-base-100/95 backdrop-blur border-primary/20">
      <CardHeader>
        <CardTitle>Informations du Chapitre</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="chapter_number">Numéro du chapitre</Label>
              <Input
                id="chapter_number"
                type="number"
                value={formData.chapter_number}
                onChange={(e) => setFormData(prev => ({ ...prev, chapter_number: parseInt(e.target.value) || 1 }))}
                min="1"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="featured_image">Image de couverture (URL)</Label>
              <Input
                id="featured_image"
                value={formData.featured_image}
                onChange={(e) => setFormData(prev => ({ ...prev, featured_image: e.target.value }))}
                placeholder="/images/chapter1.jpg"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="title">Titre</Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) => handleTitleChange(e.target.value)}
              placeholder="Ex: La Fondation du Royaume"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="slug">Slug (URL)</Label>
            <Input
              id="slug"
              value={formData.slug}
              onChange={(e) => setFormData(prev => ({ ...prev, slug: e.target.value }))}
              placeholder="fondation-royaume"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="excerpt">Extrait (résumé court)</Label>
            <Textarea
              id="excerpt"
              value={formData.excerpt}
              onChange={(e) => setFormData(prev => ({ ...prev, excerpt: e.target.value }))}
              placeholder="Un court résumé de ce chapitre..."
              rows={2}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="content">Contenu du chapitre</Label>
            <Textarea
              id="content"
              value={formData.content}
              onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
              placeholder="Le contenu complet du chapitre..."
              rows={8}
              required
            />
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              id="published"
              checked={formData.is_published}
              onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_published: checked }))}
            />
            <Label htmlFor="published">Publier ce chapitre</Label>
          </div>

          <Separator />

          <div className="flex gap-2">
            <Button type="submit" disabled={saving}>
              {saving ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Sauvegarde...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Sauvegarder
                </>
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
