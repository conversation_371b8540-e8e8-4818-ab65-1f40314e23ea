"use client"

import { useState, useEffect, useCallback } from 'react'
import {
  ReglementSection,
  ReglementRule,
  LoreChapter,
  Ticket,
  TicketCategory,
  TicketComment,
  contentService,
  adminService
} from '@/lib/supabase'



// Custom hook for content management
export function useContent() {
  const [reglementSections, setReglementSections] = useState<ReglementSection[]>([])
  const [loreChapters, setLoreChapters] = useState<LoreChapter[]>([])
  const [ticketCategories, setTicketCategories] = useState<TicketCategory[]>([])
  const [userTickets, setUserTickets] = useState<Ticket[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Fetch règlement sections
  const fetchReglementSections = useCallback(async () => {
    setLoading(true)
    setError(null)

    try {
      const data = await contentService.getReglementSections()
      setReglementSections(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch règlement sections')
    } finally {
      setLoading(false)
    }
  }, [])

  // Fetch lore chapters
  const fetchLoreChapters = useCallback(async () => {
    setLoading(true)
    setError(null)

    try {
      const data = await contentService.getLoreChapters()
      setLoreChapters(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch lore chapters')
    } finally {
      setLoading(false)
    }
  }, [])

  // Fetch ticket categories
  const fetchTicketCategories = useCallback(async () => {
    try {
      const data = await contentService.getTicketCategories()
      setTicketCategories(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch ticket categories')
    }
  }, [])

  // Fetch user tickets
  const fetchUserTickets = useCallback(async (userId: string) => {
    setLoading(true)
    setError(null)

    try {
      const data = await contentService.getUserTickets(userId)
      setUserTickets(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch user tickets')
    } finally {
      setLoading(false)
    }
  }, [])

  // Create new ticket
  const createTicket = useCallback(async (ticket: Omit<Ticket, 'id' | 'created_at' | 'updated_at'>) => {
    setLoading(true)
    setError(null)

    try {
      const data = await contentService.createTicket(ticket)
      // Refresh user tickets
      if (ticket.created_by) {
        await fetchUserTickets(ticket.created_by)
      }
      return data
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create ticket')
      return null
    } finally {
      setLoading(false)
    }
  }, [fetchUserTickets])

  // Get specific lore chapter
  const getLoreChapter = useCallback(async (slug: string) => {
    setLoading(true)
    setError(null)

    try {
      const data = await contentService.getLoreChapter(slug)
      return data
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch lore chapter')
      return null
    } finally {
      setLoading(false)
    }
  }, [])

  // Get ticket details
  const getTicket = useCallback(async (id: string) => {
    setLoading(true)
    setError(null)

    try {
      const data = await contentService.getTicket(id)
      return data
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch ticket')
      return null
    } finally {
      setLoading(false)
    }
  }, [])

  // Get ticket comments
  const getTicketComments = useCallback(async (ticketId: string) => {
    try {
      const data = await contentService.getTicketComments(ticketId)
      return data
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch ticket comments')
      return []
    }
  }, [])

  // Add ticket comment
  const addTicketComment = useCallback(async (comment: Omit<TicketComment, 'id' | 'created_at' | 'updated_at'>) => {
    try {
      const data = await contentService.addTicketComment(comment)
      return data
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to add comment')
      return null
    }
  }, [])

  // Initialize data on mount
  useEffect(() => {
    fetchReglementSections()
    fetchLoreChapters()
    fetchTicketCategories()
  }, [fetchReglementSections, fetchLoreChapters, fetchTicketCategories])

  return {
    // Data
    reglementSections,
    loreChapters,
    ticketCategories,
    userTickets,

    // State
    loading,
    error,

    // Actions
    fetchReglementSections,
    fetchLoreChapters,
    fetchTicketCategories,
    fetchUserTickets,
    createTicket,
    getLoreChapter,
    getTicket,
    getTicketComments,
    addTicketComment,

    // Utilities
    clearError: () => setError(null),
  }
}

// Custom hook for admin content management
export function useAdminContent() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Admin règlement management
  const getAllReglementSections = useCallback(async () => {
    setLoading(true)
    setError(null)

    try {
      const data = await adminService.getAllReglementSections()
      return data
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch règlement sections')
      return []
    } finally {
      setLoading(false)
    }
  }, [])

  const createReglementSection = useCallback(async (section: Omit<ReglementSection, 'id' | 'created_at' | 'updated_at'>) => {
    setLoading(true)
    setError(null)

    try {
      const data = await adminService.createReglementSection(section)
      return data
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create règlement section')
      return null
    } finally {
      setLoading(false)
    }
  }, [])

  const updateReglementSection = useCallback(async (id: string, updates: Partial<ReglementSection>) => {
    setLoading(true)
    setError(null)

    try {
      const data = await adminService.updateReglementSection(id, updates)
      return data
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update règlement section')
      return null
    } finally {
      setLoading(false)
    }
  }, [])

  // Admin lore management
  const getAllLoreChapters = useCallback(async () => {
    setLoading(true)
    setError(null)

    try {
      const data = await adminService.getAllLoreChapters()
      return data
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch lore chapters')
      return []
    } finally {
      setLoading(false)
    }
  }, [])

  const createLoreChapter = useCallback(async (chapter: Omit<LoreChapter, 'id' | 'created_at' | 'updated_at'>) => {
    setLoading(true)
    setError(null)

    try {
      const data = await adminService.createLoreChapter(chapter)
      return data
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create lore chapter')
      return null
    } finally {
      setLoading(false)
    }
  }, [])

  const updateLoreChapter = useCallback(async (id: string, updates: Partial<LoreChapter>) => {
    setLoading(true)
    setError(null)

    try {
      const data = await adminService.updateLoreChapter(id, updates)
      return data
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update lore chapter')
      return null
    } finally {
      setLoading(false)
    }
  }, [])

  // Admin ticket management
  const getAllTickets = useCallback(async () => {
    setLoading(true)
    setError(null)

    try {
      const data = await adminService.getAllTickets()
      return data
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch tickets')
      return []
    } finally {
      setLoading(false)
    }
  }, [])

  const updateTicket = useCallback(async (id: string, updates: Partial<Ticket>) => {
    setLoading(true)
    setError(null)

    try {
      const data = await adminService.updateTicket(id, updates)
      return data
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update ticket')
      return null
    } finally {
      setLoading(false)
    }
  }, [])

  const getDashboardStats = useCallback(async () => {
    try {
      const data = await adminService.getDashboardStats()
      return data
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch dashboard stats')
      return null
    }
  }, [])

  return {
    // State
    loading,
    error,

    // Règlement Actions
    getAllReglementSections,
    createReglementSection,
    updateReglementSection,

    // Lore Actions
    getAllLoreChapters,
    createLoreChapter,
    updateLoreChapter,

    // Ticket Actions
    getAllTickets,
    updateTicket,
    getDashboardStats,

    // Utilities
    clearError: () => setError(null),
  }
}
