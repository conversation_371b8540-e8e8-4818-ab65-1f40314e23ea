"use client"

import { useEffect, useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from "@/components/ui/breadcrumb"
import { 
  FileText, 
  Plus, 
  Edit, 
  Trash2, 
  Eye, 
  EyeOff, 
  Save,
  ArrowLeft,
  AlertTriangle,
  CheckCircle,
  Loader2
} from "lucide-react"
import { motion } from "framer-motion"
import { useAdminContent } from "@/hooks/use-stock"
import { ReglementSection } from "@/lib/supabase"

export default function AdminReglementPage() {
  const [sections, setSections] = useState<ReglementSection[]>([])
  const [editingSection, setEditingSection] = useState<ReglementSection | null>(null)
  const [isCreating, setIsCreating] = useState(false)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)

  const { 
    getAllReglementSections, 
    createReglementSection, 
    updateReglementSection,
    loading: adminLoading 
  } = useAdminContent()

  useEffect(() => {
    loadSections()
  }, [])

  const loadSections = async () => {
    try {
      setLoading(true)
      const data = await getAllReglementSections()
      setSections(data)
    } catch (err) {
      setError('Erreur lors du chargement des sections')
    } finally {
      setLoading(false)
    }
  }

  const handleSave = async (sectionData: Partial<ReglementSection>) => {
    try {
      setSaving(true)
      setError(null)
      
      if (editingSection) {
        // Update existing section
        await updateReglementSection(editingSection.id, sectionData)
        setSuccess('Section mise à jour avec succès')
      } else {
        // Create new section
        await createReglementSection({
          title: sectionData.title || '',
          slug: sectionData.slug || '',
          description: sectionData.description || '',
          order_index: sectionData.order_index || 0,
          is_published: sectionData.is_published || false
        })
        setSuccess('Section créée avec succès')
      }
      
      await loadSections()
      setEditingSection(null)
      setIsCreating(false)
    } catch (err) {
      setError('Erreur lors de la sauvegarde')
    } finally {
      setSaving(false)
    }
  }

  const togglePublished = async (section: ReglementSection) => {
    try {
      await updateReglementSection(section.id, {
        is_published: !section.is_published
      })
      await loadSections()
      setSuccess(`Section ${section.is_published ? 'dépubliée' : 'publiée'} avec succès`)
    } catch (err) {
      setError('Erreur lors de la modification du statut')
    }
  }

  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '')
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim()
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin mx-auto" />
          <p>Chargement des sections...</p>
        </div>
      </div>
    )
  }

  return (
    <div>
      <header className="flex h-16 shrink-0 items-center gap-2 border-b border-base-300/50 bg-base-100/95 backdrop-blur px-4 shadow-sm">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/admin" className="text-base-content/60 hover:text-base-content">
                Administration
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage className="text-base-content font-medium">Gestion du Règlement</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
        
        <div className="ml-auto flex items-center gap-2">
          <Button variant="outline" size="sm" asChild>
            <a href="/reglement">
              <Eye className="h-4 w-4 mr-2" />
              Voir Public
            </a>
          </Button>
          <Button 
            size="sm" 
            onClick={() => setIsCreating(true)}
            disabled={isCreating || editingSection !== null}
          >
            <Plus className="h-4 w-4 mr-2" />
            Nouvelle Section
          </Button>
        </div>
      </header>

      <main className="p-6 max-w-7xl mx-auto">
        {/* Messages */}
        {error && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center gap-2"
          >
            <AlertTriangle className="h-5 w-5 text-red-500" />
            <span className="text-red-700">{error}</span>
            <Button variant="ghost" size="sm" onClick={() => setError(null)} className="ml-auto">
              ×
            </Button>
          </motion.div>
        )}

        {success && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg flex items-center gap-2"
          >
            <CheckCircle className="h-5 w-5 text-green-500" />
            <span className="text-green-700">{success}</span>
            <Button variant="ghost" size="sm" onClick={() => setSuccess(null)} className="ml-auto">
              ×
            </Button>
          </motion.div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Liste des sections */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold">Sections du Règlement</h2>
              <Badge variant="outline">{sections.length} sections</Badge>
            </div>

            <div className="space-y-3">
              {sections.map((section) => (
                <motion.div
                  key={section.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  whileHover={{ scale: 1.02 }}
                >
                  <Card className="bg-base-100/95 backdrop-blur border-primary/20">
                    <CardHeader className="pb-3">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <CardTitle className="text-lg">{section.title}</CardTitle>
                          <CardDescription className="text-sm">
                            {section.description || 'Aucune description'}
                          </CardDescription>
                          <div className="flex items-center gap-2 mt-2">
                            <Badge variant={section.is_published ? "default" : "secondary"}>
                              {section.is_published ? 'Publié' : 'Brouillon'}
                            </Badge>
                            <Badge variant="outline">Ordre: {section.order_index}</Badge>
                          </div>
                        </div>
                        <div className="flex items-center gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => togglePublished(section)}
                          >
                            {section.is_published ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setEditingSection(section)}
                            disabled={isCreating}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardHeader>
                  </Card>
                </motion.div>
              ))}

              {sections.length === 0 && (
                <Card className="bg-base-100/95 backdrop-blur border-dashed border-primary/30">
                  <CardContent className="p-8 text-center">
                    <FileText className="h-12 w-12 text-base-content/50 mx-auto mb-4" />
                    <h3 className="text-lg font-medium mb-2">Aucune section</h3>
                    <p className="text-base-content/70 mb-4">
                      Commencez par créer votre première section de règlement.
                    </p>
                    <Button onClick={() => setIsCreating(true)}>
                      <Plus className="h-4 w-4 mr-2" />
                      Créer une section
                    </Button>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>

          {/* Formulaire d'édition */}
          {(editingSection || isCreating) && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h2 className="text-2xl font-bold">
                  {editingSection ? 'Modifier la Section' : 'Nouvelle Section'}
                </h2>
                <Button
                  variant="outline"
                  onClick={() => {
                    setEditingSection(null)
                    setIsCreating(false)
                  }}
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Retour
                </Button>
              </div>

              <SectionForm
                section={editingSection}
                onSave={handleSave}
                saving={saving}
                generateSlug={generateSlug}
              />
            </div>
          )}
        </div>
      </main>
    </div>
  )
}

// Composant formulaire séparé
function SectionForm({ 
  section, 
  onSave, 
  saving, 
  generateSlug 
}: { 
  section: ReglementSection | null
  onSave: (data: Partial<ReglementSection>) => void
  saving: boolean
  generateSlug: (title: string) => string
}) {
  const [formData, setFormData] = useState({
    title: section?.title || '',
    slug: section?.slug || '',
    description: section?.description || '',
    order_index: section?.order_index || 0,
    is_published: section?.is_published || false
  })

  const handleTitleChange = (title: string) => {
    setFormData(prev => ({
      ...prev,
      title,
      slug: prev.slug || generateSlug(title)
    }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSave(formData)
  }

  return (
    <Card className="bg-base-100/95 backdrop-blur border-primary/20">
      <CardHeader>
        <CardTitle>Informations de la Section</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="title">Titre</Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) => handleTitleChange(e.target.value)}
              placeholder="Ex: Règles Générales"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="slug">Slug (URL)</Label>
            <Input
              id="slug"
              value={formData.slug}
              onChange={(e) => setFormData(prev => ({ ...prev, slug: e.target.value }))}
              placeholder="regles-generales"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              placeholder="Description de cette section..."
              rows={3}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="order">Ordre d'affichage</Label>
            <Input
              id="order"
              type="number"
              value={formData.order_index}
              onChange={(e) => setFormData(prev => ({ ...prev, order_index: parseInt(e.target.value) || 0 }))}
              min="0"
            />
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              id="published"
              checked={formData.is_published}
              onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_published: checked }))}
            />
            <Label htmlFor="published">Publier cette section</Label>
          </div>

          <Separator />

          <div className="flex gap-2">
            <Button type="submit" disabled={saving}>
              {saving ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Sauvegarde...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Sauvegarder
                </>
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
