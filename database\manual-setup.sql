-- Manual Database Setup for Le Royaume Des Tempêtes
-- Copy and paste this into your Supabase SQL Editor
-- Run each section separately if needed

-- ============================================================================
-- STEP 1: Drop old stock management tables (if they exist)
-- ============================================================================

DROP VIEW IF EXISTS stock_summary CASCADE;
DROP VIEW IF EXISTS stock_items_with_category CASCADE;
DROP TABLE IF EXISTS stock_alerts CASCADE;
DROP TABLE IF EXISTS stock_transactions CASCADE;
DROP TABLE IF EXISTS stock_items CASCADE;
DROP TABLE IF EXISTS stock_categories CASCADE;

-- ============================================================================
-- STEP 2: Create new content management tables
-- ============================================================================

-- Create règlement (rules) sections table
CREATE TABLE IF NOT EXISTS reglement_sections (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    order_index INTEGER NOT NULL DEFAULT 0,
    is_published BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by VARCHAR(255),
    updated_by VARCHAR(255)
);

-- Create règlement rules table
CREATE TABLE IF NOT EXISTS reglement_rules (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    section_id UUID NOT NULL REFERENCES reglement_sections(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    order_index INTEGER NOT NULL DEFAULT 0,
    is_published BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by VARCHAR(255),
    updated_by VARCHAR(255)
);

-- Create lore chapters table
CREATE TABLE IF NOT EXISTS lore_chapters (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL UNIQUE,
    content TEXT NOT NULL,
    excerpt TEXT,
    chapter_number INTEGER NOT NULL,
    is_published BOOLEAN NOT NULL DEFAULT false,
    featured_image VARCHAR(500),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by VARCHAR(255),
    updated_by VARCHAR(255)
);

-- Create ticket categories table
CREATE TABLE IF NOT EXISTS ticket_categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    color VARCHAR(7),
    icon VARCHAR(50),
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create tickets table
CREATE TABLE IF NOT EXISTS tickets (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    category_id UUID NOT NULL REFERENCES ticket_categories(id) ON DELETE RESTRICT,
    status VARCHAR(20) NOT NULL DEFAULT 'open' CHECK (status IN ('open', 'in_progress', 'resolved', 'closed')),
    priority VARCHAR(10) NOT NULL DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    created_by VARCHAR(255) NOT NULL,
    assigned_to VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    resolved_at TIMESTAMP WITH TIME ZONE,
    resolved_by VARCHAR(255)
);

-- Create ticket comments table
CREATE TABLE IF NOT EXISTS ticket_comments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    ticket_id UUID NOT NULL REFERENCES tickets(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    is_internal BOOLEAN NOT NULL DEFAULT false,
    created_by VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create admin users table
CREATE TABLE IF NOT EXISTS admin_users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    discord_id VARCHAR(255) NOT NULL UNIQUE,
    username VARCHAR(255) NOT NULL,
    avatar_url VARCHAR(500),
    role VARCHAR(20) NOT NULL DEFAULT 'moderator' CHECK (role IN ('admin', 'moderator', 'editor')),
    permissions JSONB,
    is_active BOOLEAN NOT NULL DEFAULT true,
    last_login TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================================================
-- STEP 3: Create indexes for better performance
-- ============================================================================

CREATE INDEX IF NOT EXISTS idx_reglement_sections_published ON reglement_sections(is_published);
CREATE INDEX IF NOT EXISTS idx_reglement_sections_order ON reglement_sections(order_index);
CREATE INDEX IF NOT EXISTS idx_reglement_rules_section ON reglement_rules(section_id);
CREATE INDEX IF NOT EXISTS idx_reglement_rules_published ON reglement_rules(is_published);
CREATE INDEX IF NOT EXISTS idx_reglement_rules_order ON reglement_rules(order_index);
CREATE INDEX IF NOT EXISTS idx_lore_chapters_published ON lore_chapters(is_published);
CREATE INDEX IF NOT EXISTS idx_lore_chapters_number ON lore_chapters(chapter_number);
CREATE INDEX IF NOT EXISTS idx_tickets_status ON tickets(status);
CREATE INDEX IF NOT EXISTS idx_tickets_priority ON tickets(priority);
CREATE INDEX IF NOT EXISTS idx_tickets_category ON tickets(category_id);
CREATE INDEX IF NOT EXISTS idx_tickets_created_by ON tickets(created_by);
CREATE INDEX IF NOT EXISTS idx_tickets_assigned_to ON tickets(assigned_to);
CREATE INDEX IF NOT EXISTS idx_ticket_comments_ticket ON ticket_comments(ticket_id);
CREATE INDEX IF NOT EXISTS idx_admin_users_discord_id ON admin_users(discord_id);
CREATE INDEX IF NOT EXISTS idx_admin_users_role ON admin_users(role);

-- ============================================================================
-- STEP 4: Insert sample data
-- ============================================================================

-- Insert default ticket categories
INSERT INTO ticket_categories (name, description, color, icon) VALUES
('Bug Report', 'Signalement de bugs et problèmes techniques', '#FF6B6B', 'Bug'),
('Feature Request', 'Demandes de nouvelles fonctionnalités', '#4ECDC4', 'Lightbulb'),
('Support', 'Demandes d''aide et support général', '#45B7D1', 'HelpCircle'),
('Modération', 'Questions liées à la modération', '#96CEB4', 'Shield'),
('Événement', 'Organisation et gestion d''événements', '#FFEAA7', 'Calendar'),
('Autre', 'Autres demandes non catégorisées', '#A0A0A0', 'MessageSquare')
ON CONFLICT (name) DO NOTHING;

-- Insert sample règlement sections
INSERT INTO reglement_sections (title, slug, description, order_index, is_published) VALUES
('Règles Générales', 'regles-generales', 'Règles de base du serveur Discord', 1, true),
('Règles de Chat', 'regles-chat', 'Règles spécifiques aux canaux de discussion', 2, true),
('Règles de Jeu', 'regles-jeu', 'Règles pour les activités de jeu', 3, true),
('Sanctions', 'sanctions', 'Système de sanctions et procédures', 4, true)
ON CONFLICT (slug) DO NOTHING;

-- Insert sample lore chapters
INSERT INTO lore_chapters (title, slug, content, excerpt, chapter_number, is_published, featured_image) VALUES
('La Fondation du Royaume', 'fondation-royaume', 
 'Il y a des siècles, dans les terres mystérieuses où les tempêtes magiques dansent éternellement, un royaume fut fondé par des héros légendaires. Ces terres, battues par des vents surnaturels et illuminées par des éclairs perpétuels, abritaient des secrets anciens et des pouvoirs oubliés.

Les premiers habitants de ces terres étaient des nomades, des aventuriers et des mages qui cherchaient à comprendre la nature des tempêtes magiques. Ils découvrirent que ces phénomènes n''étaient pas naturels, mais le résultat d''une ancienne magie qui imprégnait la terre elle-même.

C''est ainsi que naquit le Royaume des Tempêtes, un lieu où la magie et la nature se mélangent dans une danse éternelle de pouvoir et de mystère.', 
 'L''histoire de la création du Royaume des Tempêtes et de ses premiers dirigeants.', 
 1, true, '/images/chapter1.jpg'),
('Les Grandes Tempêtes', 'grandes-tempetes', 
 'Les tempêtes qui donnent son nom au royaume ne sont pas ordinaires. Elles sont imprégnées d''une magie ancienne qui remonte aux premiers âges du monde. Ces tempêtes magiques apparaissent de manière cyclique, apportant avec elles des changements profonds dans le royaume.

Chaque tempête a sa propre personnalité et ses propres effets. Certaines apportent la guérison et la croissance, tandis que d''autres peuvent transformer le paysage ou révéler des secrets cachés. Les habitants du royaume ont appris à lire les signes et à se préparer à ces événements extraordinaires.

Les mages du royaume étudient ces phénomènes depuis des générations, cherchant à comprendre leur origine et leur but. Leurs recherches ont révélé que les tempêtes sont liées à un réseau complexe de lignes de force magiques qui traversent tout le royaume.', 
 'Découvrez les mystères des tempêtes magiques qui façonnent le royaume.', 
 2, true, '/images/chapter2.jpg')
ON CONFLICT (slug) DO NOTHING;

-- ============================================================================
-- STEP 5: Enable Row Level Security (Optional - for production)
-- ============================================================================

-- Uncomment these lines when you're ready to enable security
-- ALTER TABLE reglement_sections ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE reglement_rules ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE lore_chapters ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE ticket_categories ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE tickets ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE ticket_comments ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE admin_users ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- STEP 6: Create helpful views
-- ============================================================================

CREATE OR REPLACE VIEW reglement_complete AS
SELECT 
    rs.id as section_id,
    rs.title as section_title,
    rs.slug as section_slug,
    rs.description as section_description,
    rs.order_index as section_order,
    rr.id as rule_id,
    rr.title as rule_title,
    rr.content as rule_content,
    rr.order_index as rule_order,
    rr.is_published as rule_published
FROM reglement_sections rs
LEFT JOIN reglement_rules rr ON rs.id = rr.section_id
WHERE rs.is_published = true
ORDER BY rs.order_index, rr.order_index;

CREATE OR REPLACE VIEW lore_published AS
SELECT 
    id,
    title,
    slug,
    content,
    excerpt,
    chapter_number,
    featured_image,
    created_at,
    updated_at
FROM lore_chapters
WHERE is_published = true
ORDER BY chapter_number;

CREATE OR REPLACE VIEW tickets_with_details AS
SELECT 
    t.id,
    t.title,
    t.description,
    t.status,
    t.priority,
    t.created_by,
    t.assigned_to,
    t.created_at,
    t.updated_at,
    t.resolved_at,
    t.resolved_by,
    tc.name as category_name,
    tc.color as category_color,
    tc.icon as category_icon,
    COUNT(tcm.id) as comment_count
FROM tickets t
JOIN ticket_categories tc ON t.category_id = tc.id
LEFT JOIN ticket_comments tcm ON t.id = tcm.ticket_id
GROUP BY t.id, tc.name, tc.color, tc.icon
ORDER BY t.created_at DESC;

-- ============================================================================
-- SETUP COMPLETE!
-- ============================================================================
-- Your content management database is now ready to use.
-- You can now run your Next.js application and test the functionality.
