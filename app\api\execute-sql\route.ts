import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const { sql, description } = await request.json()
    
    if (!sql) {
      return NextResponse.json(
        { success: false, error: 'SQL query is required' },
        { status: 400 }
      )
    }

    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

    if (!supabaseUrl || !supabaseServiceKey) {
      return NextResponse.json(
        { success: false, error: 'Supabase configuration missing' },
        { status: 500 }
      )
    }

    // Try to execute SQL using direct HTTP request to Supabase
    // Since your instance might not have the standard RPC functions
    
    // Method 1: Try using a direct SQL execution approach
    try {
      const response = await fetch(`${supabaseUrl}/rest/v1/rpc/exec`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${supabaseServiceKey}`,
          'apikey': supabaseService<PERSON>ey
        },
        body: JSON.stringify({ sql })
      })

      if (response.ok) {
        const result = await response.json()
        return NextResponse.json({ 
          success: true, 
          data: result,
          message: `${description} executed successfully`
        })
      }
    } catch (error) {
      // Method 1 failed, try alternative approaches
    }

    // Method 2: Try using the query endpoint directly
    try {
      const response = await fetch(`${supabaseUrl}/rest/v1/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/sql',
          'Authorization': `Bearer ${supabaseServiceKey}`,
          'apikey': supabaseServiceKey,
          'Prefer': 'return=minimal'
        },
        body: sql
      })

      if (response.ok) {
        return NextResponse.json({ 
          success: true, 
          message: `${description} executed successfully`
        })
      }
    } catch (error) {
      // Method 2 failed
    }

    // Method 3: Try using Supabase client with a workaround
    const { createClient } = require('@supabase/supabase-js')
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // For CREATE TABLE statements, we can try to use the from() method
    // which will fail but might give us useful error information
    if (sql.trim().toUpperCase().startsWith('CREATE TABLE')) {
      const tableName = extractTableName(sql)
      if (tableName) {
        try {
          // Try to query the table - if it fails, the table doesn't exist
          const { error } = await supabase.from(tableName).select('*').limit(1)
          
          if (error && (error.message.includes('does not exist') || error.code === 'PGRST116')) {
            return NextResponse.json({
              success: false,
              error: `Table ${tableName} does not exist. Please create it manually in Supabase SQL Editor.`,
              needsManualCreation: true,
              sql: sql
            })
          } else {
            return NextResponse.json({
              success: true,
              message: `Table ${tableName} already exists or is accessible`
            })
          }
        } catch (err) {
          return NextResponse.json({
            success: false,
            error: `Unable to verify table ${tableName}. Please create manually.`,
            needsManualCreation: true,
            sql: sql
          })
        }
      }
    }

    // For INSERT statements, try to execute them using Supabase client
    if (sql.trim().toUpperCase().startsWith('INSERT')) {
      const tableName = extractInsertTableName(sql)
      if (tableName) {
        try {
          // Parse and execute INSERT statements using Supabase client
          if (tableName === 'ticket_categories') {
            const { error } = await supabase.from('ticket_categories').upsert([
              { name: 'Bug Report', description: 'Signalement de bugs et problèmes techniques', color: '#FF6B6B', icon: 'Bug' },
              { name: 'Feature Request', description: 'Demandes de nouvelles fonctionnalités', color: '#4ECDC4', icon: 'Lightbulb' },
              { name: 'Support', description: 'Demandes d\'aide et support général', color: '#45B7D1', icon: 'HelpCircle' },
              { name: 'Modération', description: 'Questions liées à la modération', color: '#96CEB4', icon: 'Shield' },
              { name: 'Événement', description: 'Organisation et gestion d\'événements', color: '#FFEAA7', icon: 'Calendar' },
              { name: 'Autre', description: 'Autres demandes non catégorisées', color: '#A0A0A0', icon: 'MessageSquare' }
            ], { onConflict: 'name' })

            if (error) throw error
            return NextResponse.json({ success: true, message: 'Ticket categories inserted successfully' })
          }

          if (tableName === 'reglement_sections') {
            const { error } = await supabase.from('reglement_sections').upsert([
              { title: 'Règles Générales', slug: 'regles-generales', description: 'Règles de base du serveur Discord', order_index: 1, is_published: true },
              { title: 'Règles de Chat', slug: 'regles-chat', description: 'Règles spécifiques aux canaux de discussion', order_index: 2, is_published: true },
              { title: 'Règles de Jeu', slug: 'regles-jeu', description: 'Règles pour les activités de jeu', order_index: 3, is_published: true },
              { title: 'Sanctions', slug: 'sanctions', description: 'Système de sanctions et procédures', order_index: 4, is_published: true }
            ], { onConflict: 'slug' })

            if (error) throw error
            return NextResponse.json({ success: true, message: 'Règlement sections inserted successfully' })
          }

          if (tableName === 'lore_chapters') {
            const { error } = await supabase.from('lore_chapters').upsert([
              {
                title: 'La Fondation du Royaume',
                slug: 'fondation-royaume',
                content: 'Il y a des siècles, dans les terres mystérieuses où les tempêtes magiques dansent éternellement, un royaume fut fondé par des héros légendaires. Ces terres, battues par des vents surnaturels et illuminées par des éclairs perpétuels, abritaient des secrets anciens et des pouvoirs oubliés.\n\nLes premiers habitants de ces terres étaient des nomades, des aventuriers et des mages qui cherchaient à comprendre la nature des tempêtes magiques. Ils découvrirent que ces phénomènes n\'étaient pas naturels, mais le résultat d\'une ancienne magie qui imprégnait la terre elle-même.\n\nC\'est ainsi que naquit le Royaume des Tempêtes, un lieu où la magie et la nature se mélangent dans une danse éternelle de pouvoir et de mystère.',
                excerpt: 'L\'histoire de la création du Royaume des Tempêtes et de ses premiers dirigeants.',
                chapter_number: 1,
                is_published: true,
                featured_image: '/images/chapter1.jpg'
              },
              {
                title: 'Les Grandes Tempêtes',
                slug: 'grandes-tempetes',
                content: 'Les tempêtes qui donnent son nom au royaume ne sont pas ordinaires. Elles sont imprégnées d\'une magie ancienne qui remonte aux premiers âges du monde. Ces tempêtes magiques apparaissent de manière cyclique, apportant avec elles des changements profonds dans le royaume.\n\nChaque tempête a sa propre personnalité et ses propres effets. Certaines apportent la guérison et la croissance, tandis que d\'autres peuvent transformer le paysage ou révéler des secrets cachés. Les habitants du royaume ont appris à lire les signes et à se préparer à ces événements extraordinaires.\n\nLes mages du royaume étudient ces phénomènes depuis des générations, cherchant à comprendre leur origine et leur but. Leurs recherches ont révélé que les tempêtes sont liées à un réseau complexe de lignes de force magiques qui traversent tout le royaume.',
                excerpt: 'Découvrez les mystères des tempêtes magiques qui façonnent le royaume.',
                chapter_number: 2,
                is_published: true,
                featured_image: '/images/chapter2.jpg'
              }
            ], { onConflict: 'slug' })

            if (error) throw error
            return NextResponse.json({ success: true, message: 'Lore chapters inserted successfully' })
          }

          // Fallback for other INSERT statements
          return NextResponse.json({
            success: false,
            error: `INSERT for ${tableName} not implemented. Please execute manually.`,
            needsManualCreation: true,
            sql: sql
          })

        } catch (err) {
          return NextResponse.json({
            success: false,
            error: `Failed to insert data into ${tableName}: ${err instanceof Error ? err.message : 'Unknown error'}`,
            needsManualCreation: true,
            sql: sql
          })
        }
      }
    }

    // If we get here, we couldn't execute the SQL
    return NextResponse.json({
      success: false,
      error: 'Unable to execute SQL automatically. Please use Supabase SQL Editor.',
      needsManualCreation: true,
      sql: sql
    })

  } catch (error) {
    console.error('Error executing SQL:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error',
        needsManualCreation: true
      },
      { status: 500 }
    )
  }
}

function extractTableName(sql: string): string | null {
  const match = sql.match(/CREATE\s+TABLE\s+(?:IF\s+NOT\s+EXISTS\s+)?(\w+)/i)
  return match ? match[1] : null
}

function extractInsertTableName(sql: string): string | null {
  const match = sql.match(/INSERT\s+INTO\s+(\w+)/i)
  return match ? match[1] : null
}
