import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const { sql, description } = await request.json()
    
    if (!sql) {
      return NextResponse.json(
        { success: false, error: 'SQL query is required' },
        { status: 400 }
      )
    }

    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

    if (!supabaseUrl || !supabaseServiceKey) {
      return NextResponse.json(
        { success: false, error: 'Supabase configuration missing' },
        { status: 500 }
      )
    }

    // Try to execute SQL using direct HTTP request to Supabase
    // Since your instance might not have the standard RPC functions
    
    // Method 1: Try using a direct SQL execution approach
    try {
      const response = await fetch(`${supabaseUrl}/rest/v1/rpc/exec`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${supabaseServiceKey}`,
          'apikey': supabaseService<PERSON>ey
        },
        body: JSON.stringify({ sql })
      })

      if (response.ok) {
        const result = await response.json()
        return NextResponse.json({ 
          success: true, 
          data: result,
          message: `${description} executed successfully`
        })
      }
    } catch (error) {
      // Method 1 failed, try alternative approaches
    }

    // Method 2: Try using the query endpoint directly
    try {
      const response = await fetch(`${supabaseUrl}/rest/v1/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/sql',
          'Authorization': `Bearer ${supabaseServiceKey}`,
          'apikey': supabaseServiceKey,
          'Prefer': 'return=minimal'
        },
        body: sql
      })

      if (response.ok) {
        return NextResponse.json({ 
          success: true, 
          message: `${description} executed successfully`
        })
      }
    } catch (error) {
      // Method 2 failed
    }

    // Method 3: Try using Supabase client with a workaround
    const { createClient } = require('@supabase/supabase-js')
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // For CREATE TABLE statements, we can try to use the from() method
    // which will fail but might give us useful error information
    if (sql.trim().toUpperCase().startsWith('CREATE TABLE')) {
      const tableName = extractTableName(sql)
      if (tableName) {
        try {
          // Try to query the table - if it fails, the table doesn't exist
          const { error } = await supabase.from(tableName).select('*').limit(1)
          
          if (error && (error.message.includes('does not exist') || error.code === 'PGRST116')) {
            return NextResponse.json({
              success: false,
              error: `Table ${tableName} does not exist. Please create it manually in Supabase SQL Editor.`,
              needsManualCreation: true,
              sql: sql
            })
          } else {
            return NextResponse.json({
              success: true,
              message: `Table ${tableName} already exists or is accessible`
            })
          }
        } catch (err) {
          return NextResponse.json({
            success: false,
            error: `Unable to verify table ${tableName}. Please create manually.`,
            needsManualCreation: true,
            sql: sql
          })
        }
      }
    }

    // For INSERT statements, try to execute them
    if (sql.trim().toUpperCase().startsWith('INSERT')) {
      const tableName = extractInsertTableName(sql)
      if (tableName) {
        try {
          // We can't execute arbitrary INSERT statements through the client
          // So we'll return a message indicating manual execution is needed
          return NextResponse.json({
            success: false,
            error: `INSERT statements need to be executed manually in Supabase SQL Editor.`,
            needsManualCreation: true,
            sql: sql
          })
        } catch (err) {
          return NextResponse.json({
            success: false,
            error: `Unable to execute INSERT for ${tableName}. Please execute manually.`,
            needsManualCreation: true,
            sql: sql
          })
        }
      }
    }

    // If we get here, we couldn't execute the SQL
    return NextResponse.json({
      success: false,
      error: 'Unable to execute SQL automatically. Please use Supabase SQL Editor.',
      needsManualCreation: true,
      sql: sql
    })

  } catch (error) {
    console.error('Error executing SQL:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error',
        needsManualCreation: true
      },
      { status: 500 }
    )
  }
}

function extractTableName(sql: string): string | null {
  const match = sql.match(/CREATE\s+TABLE\s+(?:IF\s+NOT\s+EXISTS\s+)?(\w+)/i)
  return match ? match[1] : null
}

function extractInsertTableName(sql: string): string | null {
  const match = sql.match(/INSERT\s+INTO\s+(\w+)/i)
  return match ? match[1] : null
}
