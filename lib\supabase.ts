import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Helper function to set current user context for RLS
export const setCurrentUser = async (discordId: string) => {
  const { error } = await supabase.rpc('set_config', {
    setting_name: 'app.current_user_id',
    setting_value: discordId,
    is_local: true
  })

  if (error) {
    console.warn('Failed to set user context:', error)
  }
}

// Helper function to clear user context
export const clearCurrentUser = async () => {
  const { error } = await supabase.rpc('set_config', {
    setting_name: 'app.current_user_id',
    setting_value: '',
    is_local: true
  })

  if (error) {
    console.warn('Failed to clear user context:', error)
  }
}

// Types for our database tables
export interface ReglementSection {
  id: string
  title: string
  slug: string
  description?: string
  order_index: number
  is_published: boolean
  created_at: string
  updated_at: string
  created_by?: string  // Discord user ID
  updated_by?: string  // Discord user ID
}

export interface ReglementRule {
  id: string
  section_id: string
  title: string
  content: string
  order_index: number
  is_published: boolean
  created_at: string
  updated_at: string
  created_by?: string  // Discord user ID
  updated_by?: string  // Discord user ID
}

export interface LoreChapter {
  id: string
  title: string
  slug: string
  content: string
  excerpt?: string
  chapter_number: number
  is_published: boolean
  featured_image?: string
  created_at: string
  updated_at: string
  created_by?: string  // Discord user ID
  updated_by?: string  // Discord user ID
}

export interface TicketCategory {
  id: string
  name: string
  description?: string
  color?: string
  icon?: string
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface Ticket {
  id: string
  title: string
  description: string
  category_id: string
  status: 'open' | 'in_progress' | 'resolved' | 'closed'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  created_by: string  // Discord user ID
  assigned_to?: string  // Discord user ID
  created_at: string
  updated_at: string
  resolved_at?: string
  resolved_by?: string  // Discord user ID
}

export interface TicketComment {
  id: string
  ticket_id: string
  content: string
  is_internal: boolean
  created_by: string  // Discord user ID
  created_at: string
  updated_at: string
}

export interface AdminUser {
  id: string
  discord_id: string
  username: string
  avatar_url?: string
  role: 'admin' | 'moderator' | 'editor'
  permissions?: any
  is_active: boolean
  last_login?: string
  created_at: string
  updated_at: string
}

// Database helper functions
export const contentService = {
  // Règlement Services
  async getReglementSections() {
    const { data, error } = await supabase
      .from('reglement_sections')
      .select('*')
      .eq('is_published', true)
      .order('order_index')

    if (error) throw error
    return data
  },

  async getReglementComplete() {
    const { data, error } = await supabase
      .from('reglement_complete')
      .select('*')

    if (error) throw error
    return data
  },

  async getReglementRulesBySection(sectionId: string) {
    const { data, error } = await supabase
      .from('reglement_rules')
      .select('*')
      .eq('section_id', sectionId)
      .eq('is_published', true)
      .order('order_index')

    if (error) throw error
    return data
  },

  // Lore Services
  async getLoreChapters() {
    const { data, error } = await supabase
      .from('lore_published')
      .select('*')

    if (error) throw error
    return data
  },

  async getLoreChapter(slug: string) {
    const { data, error } = await supabase
      .from('lore_chapters')
      .select('*')
      .eq('slug', slug)
      .eq('is_published', true)
      .single()

    if (error) throw error
    return data
  },

  // Ticket Services
  async getTicketCategories() {
    const { data, error } = await supabase
      .from('ticket_categories')
      .select('*')
      .eq('is_active', true)
      .order('name')

    if (error) throw error
    return data
  },

  async createTicket(ticket: Omit<Ticket, 'id' | 'created_at' | 'updated_at'>) {
    const { data, error } = await supabase
      .from('tickets')
      .insert(ticket)
      .select()
      .single()

    if (error) throw error
    return data
  },

  async getUserTickets(userId: string) {
    const { data, error } = await supabase
      .from('tickets_with_details')
      .select('*')
      .eq('created_by', userId)

    if (error) throw error
    return data
  },

  async getTicket(id: string) {
    const { data, error } = await supabase
      .from('tickets_with_details')
      .select('*')
      .eq('id', id)
      .single()

    if (error) throw error
    return data
  },

  async getTicketComments(ticketId: string) {
    const { data, error } = await supabase
      .from('ticket_comments')
      .select('*')
      .eq('ticket_id', ticketId)
      .order('created_at')

    if (error) throw error
    return data
  },

  async addTicketComment(comment: Omit<TicketComment, 'id' | 'created_at' | 'updated_at'>) {
    const { data, error } = await supabase
      .from('ticket_comments')
      .insert(comment)
      .select()
      .single()

    if (error) throw error
    return data
  }
}

// Admin Services (requires admin authentication)
export const adminService = {
  // Content Management
  async getAllReglementSections() {
    const { data, error } = await supabase
      .from('reglement_sections')
      .select('*')
      .order('order_index')

    if (error) throw error
    return data
  },

  async createReglementSection(section: Omit<ReglementSection, 'id' | 'created_at' | 'updated_at'>) {
    const { data, error } = await supabase
      .from('reglement_sections')
      .insert(section)
      .select()
      .single()

    if (error) throw error
    return data
  },

  async updateReglementSection(id: string, updates: Partial<ReglementSection>) {
    const { data, error } = await supabase
      .from('reglement_sections')
      .update(updates)
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data
  },

  async deleteReglementSection(id: string) {
    const { error } = await supabase
      .from('reglement_sections')
      .delete()
      .eq('id', id)

    if (error) throw error
  },

  async getAllLoreChapters() {
    const { data, error } = await supabase
      .from('lore_chapters')
      .select('*')
      .order('chapter_number')

    if (error) throw error
    return data
  },

  async createLoreChapter(chapter: Omit<LoreChapter, 'id' | 'created_at' | 'updated_at'>) {
    const { data, error } = await supabase
      .from('lore_chapters')
      .insert(chapter)
      .select()
      .single()

    if (error) throw error
    return data
  },

  async updateLoreChapter(id: string, updates: Partial<LoreChapter>) {
    const { data, error } = await supabase
      .from('lore_chapters')
      .update(updates)
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data
  },

  async deleteLoreChapter(id: string) {
    const { error } = await supabase
      .from('lore_chapters')
      .delete()
      .eq('id', id)

    if (error) throw error
  },

  // Ticket Management
  async getAllTickets() {
    const { data, error } = await supabase
      .from('tickets_with_details')
      .select('*')

    if (error) throw error
    return data
  },

  async updateTicket(id: string, updates: Partial<Ticket>) {
    const { data, error } = await supabase
      .from('tickets')
      .update(updates)
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data
  },

  async getTicketStatistics() {
    const { data, error } = await supabase
      .from('ticket_statistics')
      .select('*')

    if (error) throw error
    return data
  },

  async getDashboardStats() {
    const { data, error } = await supabase
      .from('admin_dashboard_stats')
      .select('*')
      .single()

    if (error) throw error
    return data
  },

  // Admin User Management
  async getAdminUsers() {
    const { data, error } = await supabase
      .from('admin_users')
      .select('*')
      .order('username')

    if (error) throw error
    return data
  },

  async createAdminUser(user: Omit<AdminUser, 'id' | 'created_at' | 'updated_at'>) {
    const { data, error } = await supabase
      .from('admin_users')
      .insert(user)
      .select()
      .single()

    if (error) throw error
    return data
  },

  async updateAdminUser(id: string, updates: Partial<AdminUser>) {
    const { data, error } = await supabase
      .from('admin_users')
      .update(updates)
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data
  }
}
