import { NextRequest, NextResponse } from 'next/server'
import { contentService } from '@/lib/supabase'

export async function GET() {
  try {
    const sections = await contentService.getReglementSections()
    return NextResponse.json({ success: true, data: sections })
  } catch (error) {
    console.error('Error fetching règlement sections:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch règlement sections' },
      { status: 500 }
    )
  }
}
