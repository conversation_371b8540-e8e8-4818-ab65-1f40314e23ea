"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import {
  FileText,
  Image,
  Video,
  Music,
  Plus,
  Search,
  Filter,
  Edit3,
  Eye,
  Trash2,
  Download,
  Upload,
  Calendar,
  User,
  Tag,
  Globe,
  Lock,
  Clock,
  MoreHorizontal,
  Star,
  MessageSquare,
  Share2,
  Copy,
  ExternalLink,
  Bookmark,
  Archive,
  RefreshCw,
  Settings,
  Megaphone,
  BookOpen,
  Newspaper,
  ImageIcon,
  PlayCircle,
  FileImage,
  FileVideo,
  FileAudio,
  File,
} from "lucide-react"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
}

const cardVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      type: "spring",
      stiffness: 300,
      damping: 24,
    },
  },
}

// Données mockup pour le contenu
const mockArticles = [
  {
    id: 1,
    title: "Mise à jour majeure 2.0 - Nouvelles fonctionnalités",
    type: "Article",
    status: "Publié",
    author: "Admin_Principal",
    createdAt: "2024-01-20",
    updatedAt: "2024-01-20",
    publishedAt: "2024-01-20",
    views: 1247,
    likes: 89,
    comments: 23,
    category: "Actualités",
    tags: ["mise-à-jour", "fonctionnalités", "important"],
    featured: true,
    excerpt: "Découvrez toutes les nouvelles fonctionnalités de la mise à jour 2.0 qui révolutionne l'expérience de jeu.",
    thumbnail: "/placeholder-article.jpg",
  },
  {
    id: 2,
    title: "Événement spécial - Chasse au trésor",
    type: "Événement",
    status: "Programmé",
    author: "Moderateur_Events",
    createdAt: "2024-01-18",
    updatedAt: "2024-01-19",
    publishedAt: "2024-01-25",
    views: 0,
    likes: 0,
    comments: 0,
    category: "Événements",
    tags: ["événement", "chasse-au-trésor", "récompenses"],
    featured: false,
    excerpt: "Participez à notre grande chasse au trésor et remportez des récompenses exclusives !",
    thumbnail: "/placeholder-event.jpg",
  },
  {
    id: 3,
    title: "Guide complet - Système de magie",
    type: "Guide",
    status: "Brouillon",
    author: "Redacteur_Guides",
    createdAt: "2024-01-15",
    updatedAt: "2024-01-19",
    publishedAt: null,
    views: 0,
    likes: 0,
    comments: 0,
    category: "Guides",
    tags: ["guide", "magie", "tutoriel"],
    featured: false,
    excerpt: "Apprenez à maîtriser le système de magie avec ce guide détaillé.",
    thumbnail: "/placeholder-guide.jpg",
  },
  {
    id: 4,
    title: "Maintenance programmée - 22 janvier",
    type: "Annonce",
    status: "Publié",
    author: "Admin_Technique",
    createdAt: "2024-01-19",
    updatedAt: "2024-01-19",
    publishedAt: "2024-01-19",
    views: 567,
    likes: 12,
    comments: 8,
    category: "Maintenance",
    tags: ["maintenance", "serveur", "planifié"],
    featured: false,
    excerpt: "Maintenance programmée pour améliorer les performances du serveur.",
    thumbnail: "/placeholder-maintenance.jpg",
  },
]

const mockMedia = [
  {
    id: 1,
    name: "screenshot_castle.png",
    type: "image",
    size: "2.4 MB",
    uploadedAt: "2024-01-20",
    uploadedBy: "Admin_Principal",
    url: "/media/screenshot_castle.png",
    thumbnail: "/placeholder-image.jpg",
    dimensions: "1920x1080",
    usedIn: 3,
  },
  {
    id: 2,
    name: "trailer_update_2.0.mp4",
    type: "video",
    size: "45.2 MB",
    uploadedAt: "2024-01-19",
    uploadedBy: "Content_Creator",
    url: "/media/trailer_update_2.0.mp4",
    thumbnail: "/placeholder-video.jpg",
    duration: "2:34",
    usedIn: 1,
  },
  {
    id: 3,
    name: "ambient_music.mp3",
    type: "audio",
    size: "8.7 MB",
    uploadedAt: "2024-01-18",
    uploadedBy: "Sound_Designer",
    url: "/media/ambient_music.mp3",
    thumbnail: "/placeholder-audio.jpg",
    duration: "3:45",
    usedIn: 0,
  },
  {
    id: 4,
    name: "rules_document.pdf",
    type: "document",
    size: "1.2 MB",
    uploadedAt: "2024-01-17",
    uploadedBy: "Admin_Principal",
    url: "/media/rules_document.pdf",
    thumbnail: "/placeholder-document.jpg",
    pages: 12,
    usedIn: 2,
  },
]

const mockStats = {
  totalArticles: 156,
  publishedArticles: 134,
  draftArticles: 22,
  totalViews: 45678,
  totalLikes: 2341,
  totalComments: 567,
  mediaFiles: 89,
  storageUsed: "2.3 GB",
}

export default function AdminContentPage() {
  const [activeTab, setActiveTab] = useState("articles")
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [typeFilter, setTypeFilter] = useState("all")
  const [selectedContent, setSelectedContent] = useState<typeof mockArticles[0] | null>(null)
  const [isContentDialogOpen, setIsContentDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [selectedMedia, setSelectedMedia] = useState<typeof mockMedia[0] | null>(null)
  const [isMediaDialogOpen, setIsMediaDialogOpen] = useState(false)

  const filteredArticles = mockArticles.filter(article => {
    const matchesSearch = article.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         article.excerpt.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === "all" || article.status === statusFilter
    const matchesType = typeFilter === "all" || article.type === typeFilter
    
    return matchesSearch && matchesStatus && matchesType
  })

  const filteredMedia = mockMedia.filter(media => {
    return media.name.toLowerCase().includes(searchTerm.toLowerCase())
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Publié":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
      case "Brouillon":
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"
      case "Programmé":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
      case "Archivé":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "Article":
        return <Newspaper className="h-4 w-4" />
      case "Guide":
        return <BookOpen className="h-4 w-4" />
      case "Événement":
        return <Calendar className="h-4 w-4" />
      case "Annonce":
        return <Megaphone className="h-4 w-4" />
      default:
        return <FileText className="h-4 w-4" />
    }
  }

  const getMediaIcon = (type: string) => {
    switch (type) {
      case "image":
        return <FileImage className="h-4 w-4 text-blue-500" />
      case "video":
        return <FileVideo className="h-4 w-4 text-purple-500" />
      case "audio":
        return <FileAudio className="h-4 w-4 text-green-500" />
      case "document":
        return <File className="h-4 w-4 text-red-500" />
      default:
        return <File className="h-4 w-4 text-gray-500" />
    }
  }

  const handleContentDetails = (content: typeof mockArticles[0]) => {
    setSelectedContent(content)
    setIsContentDialogOpen(true)
  }

  const handleMediaDetails = (media: typeof mockMedia[0]) => {
    setSelectedMedia(media)
    setIsMediaDialogOpen(true)
  }

  const handleDeleteContent = (content: typeof mockArticles[0]) => {
    setSelectedContent(content)
    setIsDeleteDialogOpen(true)
  }

  return (
    <>
      {/* Header */}
      <header className="flex h-16 shrink-0 items-center gap-2 border-b border-red-300/50 bg-gradient-to-r from-red-50 to-orange-50 dark:from-red-950 dark:to-orange-950 backdrop-blur supports-[backdrop-filter]:bg-red-50/80 px-4 shadow-sm relative z-10">
        <SidebarTrigger className="-ml-1 hover:bg-red-500/10 transition-colors" />
        <Separator orientation="vertical" className="mr-2 h-4 bg-red-300/50" />
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem className="hidden md:block">
              <BreadcrumbLink href="/admin" className="text-red-600/60 hover:text-red-600 transition-colors">
                Administration
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator className="hidden md:block text-red-600/30" />
            <BreadcrumbItem>
              <BreadcrumbPage className="text-red-700 font-medium flex items-center gap-2">
                <FileText className="h-4 w-4" />
                Gestion du Contenu
              </BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
        
        {/* Actions rapides */}
        <div className="ml-auto flex items-center gap-2">
          <Button variant="outline" size="sm" className="border-red-200 hover:bg-red-50">
            <Upload className="h-4 w-4 mr-2" />
            Importer
          </Button>
          <Button variant="outline" size="sm" className="border-red-200 hover:bg-red-50">
            <RefreshCw className="h-4 w-4 mr-2" />
            Actualiser
          </Button>
          <Button size="sm" className="bg-red-500 hover:bg-red-600">
            <Plus className="h-4 w-4 mr-2" />
            Nouveau contenu
          </Button>
        </div>
      </header>

      <div className="flex-1 space-y-6 p-6 min-h-screen relative overflow-hidden">
        {/* Statistiques du contenu */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="relative z-10"
        >
          <motion.div variants={cardVariants} className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-8">
            <Card className="border-blue-200 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-blue-700">Articles</CardTitle>
                <FileText className="h-4 w-4 text-blue-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-800">{mockStats.totalArticles}</div>
                <p className="text-xs text-blue-600">{mockStats.publishedArticles} publiés</p>
              </CardContent>
            </Card>

            <Card className="border-green-200 bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950 dark:to-green-900">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-green-700">Vues</CardTitle>
                <Eye className="h-4 w-4 text-green-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-800">{mockStats.totalViews.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",")}</div>
                <p className="text-xs text-green-600">Total des vues</p>
              </CardContent>
            </Card>

            <Card className="border-purple-200 bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950 dark:to-purple-900">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-purple-700">Interactions</CardTitle>
                <Star className="h-4 w-4 text-purple-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-purple-800">{mockStats.totalLikes}</div>
                <p className="text-xs text-purple-600">{mockStats.totalComments} commentaires</p>
              </CardContent>
            </Card>

            <Card className="border-orange-200 bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-950 dark:to-orange-900">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-orange-700">Médias</CardTitle>
                <Image className="h-4 w-4 text-orange-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-orange-800">{mockStats.mediaFiles}</div>
                <p className="text-xs text-orange-600">{mockStats.storageUsed} utilisés</p>
              </CardContent>
            </Card>
          </motion.div>

          {/* Onglets de contenu */}
          <motion.div variants={cardVariants}>
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Gestion du Contenu
                </CardTitle>
                <CardDescription>
                  Gérez vos articles, médias et autres contenus
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="articles" className="flex items-center gap-2">
                      <FileText className="h-4 w-4" />
                      Articles & Contenu
                    </TabsTrigger>
                    <TabsTrigger value="media" className="flex items-center gap-2">
                      <Image className="h-4 w-4" />
                      Bibliothèque Média
                    </TabsTrigger>
                  </TabsList>
                  
                  <TabsContent value="articles" className="space-y-4">
                    {/* Filtres pour articles */}
                    <div className="flex flex-col md:flex-row gap-4">
                      <div className="relative flex-1">
                        <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                        <Input
                          placeholder="Rechercher dans le contenu..."
                          value={searchTerm}
                          onChange={(e) => setSearchTerm(e.target.value)}
                          className="pl-8"
                        />
                      </div>
                      
                      <Select value={statusFilter} onValueChange={setStatusFilter}>
                        <SelectTrigger className="w-full md:w-48">
                          <SelectValue placeholder="Filtrer par statut" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">Tous les statuts</SelectItem>
                          <SelectItem value="Publié">Publié</SelectItem>
                          <SelectItem value="Brouillon">Brouillon</SelectItem>
                          <SelectItem value="Programmé">Programmé</SelectItem>
                          <SelectItem value="Archivé">Archivé</SelectItem>
                        </SelectContent>
                      </Select>
                      
                      <Select value={typeFilter} onValueChange={setTypeFilter}>
                        <SelectTrigger className="w-full md:w-48">
                          <SelectValue placeholder="Filtrer par type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">Tous les types</SelectItem>
                          <SelectItem value="Article">Article</SelectItem>
                          <SelectItem value="Guide">Guide</SelectItem>
                          <SelectItem value="Événement">Événement</SelectItem>
                          <SelectItem value="Annonce">Annonce</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Table des articles */}
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Contenu</TableHead>
                          <TableHead>Type</TableHead>
                          <TableHead>Statut</TableHead>
                          <TableHead>Auteur</TableHead>
                          <TableHead>Statistiques</TableHead>
                          <TableHead>Date</TableHead>
                          <TableHead>Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {filteredArticles.map((article) => (
                          <TableRow key={article.id}>
                            <TableCell>
                              <div className="flex items-start gap-3">
                                <div className="w-16 h-12 bg-muted rounded overflow-hidden flex-shrink-0">
                                  <img 
                                    src={article.thumbnail} 
                                    alt={article.title}
                                    className="w-full h-full object-cover"
                                  />
                                </div>
                                <div className="min-w-0 flex-1">
                                  <div className="font-medium flex items-center gap-2">
                                    {article.featured && <Star className="h-4 w-4 text-yellow-500 fill-current" />}
                                    <span className="truncate">{article.title}</span>
                                  </div>
                                  <div className="text-sm text-muted-foreground line-clamp-2">
                                    {article.excerpt}
                                  </div>
                                  <div className="flex flex-wrap gap-1 mt-1">
                                    {article.tags.slice(0, 2).map((tag) => (
                                      <Badge key={tag} variant="outline" className="text-xs">
                                        {tag}
                                      </Badge>
                                    ))}
                                    {article.tags.length > 2 && (
                                      <Badge variant="outline" className="text-xs">
                                        +{article.tags.length - 2}
                                      </Badge>
                                    )}
                                  </div>
                                </div>
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center gap-2">
                                {getTypeIcon(article.type)}
                                <Badge variant="outline">{article.type}</Badge>
                              </div>
                            </TableCell>
                            <TableCell>
                              <Badge className={getStatusColor(article.status)}>
                                {article.status}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center gap-2">
                                <Avatar className="h-6 w-6">
                                  <AvatarFallback className="text-xs">
                                    {article.author.slice(0, 2).toUpperCase()}
                                  </AvatarFallback>
                                </Avatar>
                                <span className="text-sm">{article.author}</span>
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="text-sm space-y-1">
                                <div className="flex items-center gap-1">
                                  <Eye className="h-3 w-3" />
                                  <span>{article.views}</span>
                                </div>
                                <div className="flex items-center gap-1">
                                  <Star className="h-3 w-3" />
                                  <span>{article.likes}</span>
                                </div>
                                <div className="flex items-center gap-1">
                                  <MessageSquare className="h-3 w-3" />
                                  <span>{article.comments}</span>
                                </div>
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="text-sm">
                                <div>Créé: {new Date(article.createdAt).toLocaleDateString('fr-FR')}</div>
                                {article.publishedAt && (
                                  <div className="text-muted-foreground">
                                    Publié: {new Date(article.publishedAt).toLocaleDateString('fr-FR')}
                                  </div>
                                )}
                              </div>
                            </TableCell>
                            <TableCell>
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="sm">
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                  <DropdownMenuItem onClick={() => handleContentDetails(article)}>
                                    <Eye className="h-4 w-4 mr-2" />
                                    Voir détails
                                  </DropdownMenuItem>
                                  <DropdownMenuItem>
                                    <Edit3 className="h-4 w-4 mr-2" />
                                    Modifier
                                  </DropdownMenuItem>
                                  <DropdownMenuItem>
                                    <Copy className="h-4 w-4 mr-2" />
                                    Dupliquer
                                  </DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem>
                                    <Share2 className="h-4 w-4 mr-2" />
                                    Partager
                                  </DropdownMenuItem>
                                  <DropdownMenuItem>
                                    <Archive className="h-4 w-4 mr-2" />
                                    Archiver
                                  </DropdownMenuItem>
                                  <DropdownMenuItem 
                                    onClick={() => handleDeleteContent(article)}
                                    className="text-red-600"
                                  >
                                    <Trash2 className="h-4 w-4 mr-2" />
                                    Supprimer
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TabsContent>
                  
                  <TabsContent value="media" className="space-y-4">
                    {/* Filtres pour médias */}
                    <div className="flex flex-col md:flex-row gap-4">
                      <div className="relative flex-1">
                        <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                        <Input
                          placeholder="Rechercher dans les médias..."
                          value={searchTerm}
                          onChange={(e) => setSearchTerm(e.target.value)}
                          className="pl-8"
                        />
                      </div>
                      
                      <Button variant="outline" className="flex items-center gap-2">
                        <Upload className="h-4 w-4" />
                        Télécharger des fichiers
                      </Button>
                    </div>

                    {/* Grille des médias */}
                    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
                      {filteredMedia.map((media) => (
                        <Card key={media.id} className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => handleMediaDetails(media)}>
                          <CardContent className="p-3">
                            <div className="aspect-square bg-muted rounded mb-2 flex items-center justify-center overflow-hidden">
                              {media.type === "image" ? (
                                <img 
                                  src={media.thumbnail} 
                                  alt={media.name}
                                  className="w-full h-full object-cover"
                                />
                              ) : (
                                <div className="flex flex-col items-center gap-2">
                                  {getMediaIcon(media.type)}
                                  <span className="text-xs text-muted-foreground">
                                    {media.type.toUpperCase()}
                                  </span>
                                </div>
                              )}
                            </div>
                            <div className="space-y-1">
                              <div className="text-sm font-medium truncate" title={media.name}>
                                {media.name}
                              </div>
                              <div className="text-xs text-muted-foreground">
                                {media.size}
                              </div>
                              <div className="text-xs text-muted-foreground">
                                Utilisé {media.usedIn} fois
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          </motion.div>
        </motion.div>

        {/* Dialog détails contenu */}
        <Dialog open={isContentDialogOpen} onOpenChange={setIsContentDialogOpen}>
          <DialogContent className="max-w-3xl">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                {selectedContent && getTypeIcon(selectedContent.type)}
                Détails du contenu
              </DialogTitle>
              <DialogDescription>
                {selectedContent?.title}
              </DialogDescription>
            </DialogHeader>
            
            {selectedContent && (
              <div className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Type</Label>
                    <div className="flex items-center gap-2">
                      {getTypeIcon(selectedContent.type)}
                      <Badge variant="outline">{selectedContent.type}</Badge>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label>Statut</Label>
                    <Badge className={getStatusColor(selectedContent.status)}>
                      {selectedContent.status}
                    </Badge>
                  </div>
                  <div className="space-y-2">
                    <Label>Auteur</Label>
                    <div className="flex items-center gap-2">
                      <Avatar className="h-6 w-6">
                        <AvatarFallback className="text-xs">
                          {selectedContent.author.slice(0, 2).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <span>{selectedContent.author}</span>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label>Catégorie</Label>
                    <Badge variant="secondary">{selectedContent.category}</Badge>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label>Résumé</Label>
                  <p className="text-sm text-muted-foreground">{selectedContent.excerpt}</p>
                </div>
                
                <div className="space-y-2">
                  <Label>Tags</Label>
                  <div className="flex flex-wrap gap-1">
                    {selectedContent.tags.map((tag) => (
                      <Badge key={tag} variant="outline">{tag}</Badge>
                    ))}
                  </div>
                </div>
                
                <div className="grid grid-cols-3 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold">{selectedContent.views}</div>
                    <div className="text-sm text-muted-foreground">Vues</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold">{selectedContent.likes}</div>
                    <div className="text-sm text-muted-foreground">J'aime</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold">{selectedContent.comments}</div>
                    <div className="text-sm text-muted-foreground">Commentaires</div>
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <Label>Date de création</Label>
                    <div>{new Date(selectedContent.createdAt).toLocaleDateString('fr-FR')}</div>
                  </div>
                  <div>
                    <Label>Dernière modification</Label>
                    <div>{new Date(selectedContent.updatedAt).toLocaleDateString('fr-FR')}</div>
                  </div>
                  {selectedContent.publishedAt && (
                    <div>
                      <Label>Date de publication</Label>
                      <div>{new Date(selectedContent.publishedAt).toLocaleDateString('fr-FR')}</div>
                    </div>
                  )}
                  <div>
                    <Label>Article en vedette</Label>
                    <div className="flex items-center gap-2">
                      <Switch checked={selectedContent.featured} disabled />
                      <span>{selectedContent.featured ? "Oui" : "Non"}</span>
                    </div>
                  </div>
                </div>
              </div>
            )}
            
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsContentDialogOpen(false)}>
                Fermer
              </Button>
              <Button>
                <Edit3 className="h-4 w-4 mr-2" />
                Modifier
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Dialog détails média */}
        <Dialog open={isMediaDialogOpen} onOpenChange={setIsMediaDialogOpen}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                {selectedMedia && getMediaIcon(selectedMedia.type)}
                Détails du média
              </DialogTitle>
              <DialogDescription>
                {selectedMedia?.name}
              </DialogDescription>
            </DialogHeader>
            
            {selectedMedia && (
              <div className="space-y-4">
                <div className="aspect-video bg-muted rounded flex items-center justify-center overflow-hidden">
                  {selectedMedia.type === "image" ? (
                    <img 
                      src={selectedMedia.thumbnail} 
                      alt={selectedMedia.name}
                      className="w-full h-full object-contain"
                    />
                  ) : (
                    <div className="flex flex-col items-center gap-4">
                      {getMediaIcon(selectedMedia.type)}
                      <span className="text-lg font-medium">
                        {selectedMedia.type.toUpperCase()}
                      </span>
                    </div>
                  )}
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Nom du fichier</Label>
                    <div className="font-mono text-sm">{selectedMedia.name}</div>
                  </div>
                  <div className="space-y-2">
                    <Label>Taille</Label>
                    <div>{selectedMedia.size}</div>
                  </div>
                  <div className="space-y-2">
                    <Label>Type</Label>
                    <div className="flex items-center gap-2">
                      {getMediaIcon(selectedMedia.type)}
                      <span className="capitalize">{selectedMedia.type}</span>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label>Utilisé dans</Label>
                    <div>{selectedMedia.usedIn} contenu(s)</div>
                  </div>
                  {selectedMedia.dimensions && (
                    <div className="space-y-2">
                      <Label>Dimensions</Label>
                      <div>{selectedMedia.dimensions}</div>
                    </div>
                  )}
                  {selectedMedia.duration && (
                    <div className="space-y-2">
                      <Label>Durée</Label>
                      <div>{selectedMedia.duration}</div>
                    </div>
                  )}
                  <div className="space-y-2">
                    <Label>Téléchargé le</Label>
                    <div>{new Date(selectedMedia.uploadedAt).toLocaleDateString('fr-FR')}</div>
                  </div>
                  <div className="space-y-2">
                    <Label>Téléchargé par</Label>
                    <div>{selectedMedia.uploadedBy}</div>
                  </div>
                </div>
              </div>
            )}
            
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsMediaDialogOpen(false)}>
                Fermer
              </Button>
              <Button variant="outline">
                <Download className="h-4 w-4 mr-2" />
                Télécharger
              </Button>
              <Button variant="destructive">
                <Trash2 className="h-4 w-4 mr-2" />
                Supprimer
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Dialog confirmation suppression */}
        <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <Trash2 className="h-5 w-5 text-red-500" />
                Confirmer la suppression
              </DialogTitle>
              <DialogDescription>
                Êtes-vous sûr de vouloir supprimer "{selectedContent?.title}" ?
                Cette action est irréversible.
              </DialogDescription>
            </DialogHeader>
            
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
                Annuler
              </Button>
              <Button 
                variant="destructive"
                onClick={() => {
                  console.log(`Suppression de: ${selectedContent?.title}`)
                  setIsDeleteDialogOpen(false)
                }}
              >
                Supprimer définitivement
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </>
  )
}