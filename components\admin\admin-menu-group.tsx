"use client"

import * as React from "react"
import { AdminMenuItem } from "./admin-menu-item"
import { cn } from "@/lib/utils"
import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
} from "@/components/ui/sidebar"

type Permission = "read" | "write" | "delete" | "moderate" | "admin"

interface MenuItem {
  title: string
  url: string
  icon: React.ComponentType<{ className?: string }>
  permissions: Permission[]
  badge?: string
  badgeVariant?: "default" | "secondary" | "destructive" | "outline"
}

interface MenuGroup {
  label: string
  items: MenuItem[]
  permissions: Permission[]
}

interface AdminMenuGroupProps {
  group: MenuGroup
  userPermissions: Permission[]
  isCollapsed?: boolean
}

const hasPermission = (userPermissions: Permission[], requiredPermissions: Permission[]): boolean => {
  return requiredPermissions.some(permission => userPermissions.includes(permission))
}

export function AdminMenuGroup({ group, userPermissions, isCollapsed = false }: AdminMenuGroupProps) {
  if (!hasPermission(userPermissions, group.permissions)) {
    return null
  }

  return (
    <SidebarGroup className="mb-4">
      {!isCollapsed && (
        <SidebarGroupLabel className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">
          {group.label}
        </SidebarGroupLabel>
      )}
      <SidebarGroupContent>
        <SidebarMenu className="space-y-1">
          {group.items
            .filter(item => hasPermission(userPermissions, item.permissions))
            .map((item) => (
              <AdminMenuItem 
                key={item.title} 
                item={item} 
                userPermissions={userPermissions}
                isCollapsed={isCollapsed}
              />
            ))
          }
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>
  )
}